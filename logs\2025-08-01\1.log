2025-08-01 00:01:05 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753977665440] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 00:01:05 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753977665440] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 00:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753977665440] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 00:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 01:01:06 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753981266374] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 01:01:06 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753981266374] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 01:01:06 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753981266374] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 01:01:06 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 02:01:05 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753984865116] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 02:01:05 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753984865116] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 02:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753984865116] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 02:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 03:01:05 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753988465079] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 03:01:05 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753988465079] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 03:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753988465079] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 03:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 04:01:05 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753992065050] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 04:01:05 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753992065050] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 04:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753992065050] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 04:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 05:01:05 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753995665039] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 05:01:05 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753995665039] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 05:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753995665039] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 05:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 06:01:04 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753999264932] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 06:01:04 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753999264932] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 06:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753999264932] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 06:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 07:01:04 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1754002864821] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 07:01:04 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1754002864821] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 07:01:04 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1754002864821] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 07:01:04 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 08:01:04 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1754006464760] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 08:01:04 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1754006464760] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 08:01:04 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1754006464760] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 08:01:04 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 09:01:04 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1754010064913] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 09:01:04 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1754010064913] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 09:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1754010064913] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 09:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 10:10:08 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1754014208352] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 10:10:08 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1754014208352] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 10:10:08 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1754014208352] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 10:10:08 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 11:10:08 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1754017808710] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 11:10:08 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1754017808710] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 11:10:08 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1754017808710] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 11:10:08 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 12:10:08 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1754021408478] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 12:10:08 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1754021408478] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 12:10:08 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1754021408478] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 12:10:08 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 13:10:08 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1754025008265] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 13:10:08 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1754025008265] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 13:10:08 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1754025008265] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 13:10:08 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-08-01 14:10:08 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1754028608255] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 14:10:08 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1754028608255] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-08-01 14:10:08 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1754028608255] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-08-01 14:10:08 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
