package com.ideal.envc.service;

import com.alibaba.fastjson.JSON;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.bean.RunFlowDetailBean;
import com.ideal.envc.model.dto.ContentCustomDto;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.FileComparisonResultDto;
import com.ideal.envc.model.dto.FileInfoDto;
import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import com.ideal.envc.service.impl.HtmlComparisonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * HTML比对服务测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("HTML比对服务测试类")
public class HtmlComparisonServiceTest {

    @Mock
    private IFileComparisonService fileComparisonService;

    @Mock
    private RunFlowResultMapper runFlowResultMapper;

    @Mock
    private RunRuleMapper runRuleMapper;

    @InjectMocks
    private HtmlComparisonServiceImpl htmlComparisonService;

    private HtmlComparisonRequestDto request;
    private RunFlowResultEntity runFlowResult;
    private RunFlowDetailBean runFlowDetailBean;
    private ContentCustomDto contentCustomDto;
    private FileComparisonResultDto fileComparisonResult;

    @BeforeEach
    void setUp() {
        // 初始化请求参数
        request = new HtmlComparisonRequestDto();
        request.setFlowId(12345L);
        request.setBaselineServer("基线服务器");
        request.setTargetServer("目标服务器");
        request.setDescription("测试比对");

        // 初始化流程结果
        runFlowResult = new RunFlowResultEntity();
        runFlowResult.setFlowid(12345L);
        runFlowResult.setContent("{\"content\":\"<div>test content</div>\"}");

        // 初始化流程详情
        runFlowDetailBean = new RunFlowDetailBean();
        runFlowDetailBean.setSourceComputerName("源服务器");
        runFlowDetailBean.setTargetComputerName("目标服务器");
        runFlowDetailBean.setSourceComputerIp("***********");
        runFlowDetailBean.setTargetComputerIp("***********");
        runFlowDetailBean.setSourcePath("/test/path");
        runFlowDetailBean.setPath("/test/path");
        runFlowDetailBean.setBusinessSystemId(1L);
        runFlowDetailBean.setSourceComputerId(1L);
        runFlowDetailBean.setTargetComputerId(2L);
        runFlowDetailBean.setTargetCenterName("目标中心");
        runFlowDetailBean.setSourceCenterName("源中心");
        runFlowDetailBean.setBusinessSystemName("测试系统");

        // 初始化内容自定义DTO
        contentCustomDto = new ContentCustomDto();
        contentCustomDto.setContent("test content");
        contentCustomDto.setSourceContent("source content");
        contentCustomDto.setTargetContent("target content");

        // 初始化文件比对结果
        fileComparisonResult = new FileComparisonResultDto();
        fileComparisonResult.setBaselineServer("基线服务器");
        fileComparisonResult.setTargetServer("目标服务器");
        fileComparisonResult.setTotalSourceFiles(10);
        fileComparisonResult.setTotalTargetFiles(10);
        fileComparisonResult.setConsistentCount(8);
        fileComparisonResult.setInconsistentCount(1);
        fileComparisonResult.setMissingCount(1);
        fileComparisonResult.setExtraCount(0);
        fileComparisonResult.setConsistentRate(new BigDecimal("80.00"));
        fileComparisonResult.setInconsistentRate(new BigDecimal("10.00"));
        fileComparisonResult.setMissingRate(new BigDecimal("10.00"));
        fileComparisonResult.setExtraRate(new BigDecimal("0.00"));
        fileComparisonResult.setConsistentFiles(new ArrayList<>());
        fileComparisonResult.setInconsistentFiles(new ArrayList<>());
        fileComparisonResult.setMissingFiles(new ArrayList<>());
        fileComparisonResult.setExtraFiles(new ArrayList<>());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 成功场景")
    void testParseHtmlComparison_Success() throws ContrastBusinessException {
        // 准备mock数据
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenReturn(fileComparisonResult);

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            // 验证结果
            assertNotNull(result);
            assertEquals("基线服务器", result.getBaselineServer());
            assertEquals("目标服务器", result.getTargetServer());
            assertEquals("***********", result.getBaseServerIp());
            assertEquals("***********", result.getTargetServerIp());

            // 验证方法调用
            verify(runFlowResultMapper, times(1)).selectRunFlowResultByFlowId(12345L);
            verify(runRuleMapper, times(1)).selectRunRuleDetailByFlowId(12345L);
            verify(fileComparisonService, times(1)).compareFileContents(any(FileComparisonRequestDto.class));
        }
    }

    @ParameterizedTest
    @ValueSource(longs = {0L, -1L})
    @DisplayName("测试解析HTML比对内容 - flowId为空或无效")
    void testParseHtmlComparison_InvalidFlowId(Long flowId) {
        // 准备测试数据
        request.setFlowId(flowId == 0L ? null : flowId);

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            htmlComparisonService.parseHtmlComparison(request);
        });

        assertTrue(exception.getMessage().contains("HTML比对解析失败") || exception.getMessage().contains("flowId不能为空"));
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 流程结果不存在")
    void testParseHtmlComparison_FlowResultNotFound() {
        // 准备mock数据
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(null);

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            htmlComparisonService.parseHtmlComparison(request);
        });

        assertTrue(exception.getMessage().contains("未查询到对应的流程结果数据"));
        verify(runFlowResultMapper, times(1)).selectRunFlowResultByFlowId(12345L);
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 流程详情不存在")
    void testParseHtmlComparison_FlowDetailNotFound() {
        // 准备mock数据
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Collections.emptyList());

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            htmlComparisonService.parseHtmlComparison(request);
        });

        assertTrue(exception.getMessage().contains("根据流程ID未查询到对应设备相关信息"));
        verify(runFlowResultMapper, times(1)).selectRunFlowResultByFlowId(12345L);
        verify(runRuleMapper, times(1)).selectRunRuleDetailByFlowId(12345L);
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 流程内容为空")
    void testParseHtmlComparison_EmptyContent() {
        // 准备mock数据 - 内容为空
        runFlowResult.setContent("");
        runFlowResult.setStderr("");
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            htmlComparisonService.parseHtmlComparison(request);
        });

        assertTrue(exception.getMessage().contains("流程结果内容为空"));
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - JSON解析失败")
    void testParseHtmlComparison_JsonParseError() {
        // 准备mock数据
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(null);

            // 执行测试并验证异常
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
                htmlComparisonService.parseHtmlComparison(request);
            });

            assertTrue(exception.getMessage().contains("解析流程内容失败"));
        }
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - HTML内容为空")
    void testParseHtmlComparison_EmptyHtmlContent() {
        // 准备mock数据
        contentCustomDto.setContent("");
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试并验证异常
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
                htmlComparisonService.parseHtmlComparison(request);
            });

            assertTrue(exception.getMessage().contains("流程内容中的HTML内容为空"));
        }
    }

    @Test
    @DisplayName("测试导出HTML比对结果 - 成功场景")
    void testExportHtmlComparisonResult_Success() throws ContrastBusinessException, IOException {
        // 准备mock数据
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenReturn(fileComparisonResult);

        MockHttpServletResponse response = new MockHttpServletResponse();

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            assertDoesNotThrow(() -> {
                htmlComparisonService.exportHtmlComparisonResult(request, response);
            });

            // 验证响应头
            assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",
                    response.getContentType());
            assertEquals("utf-8", response.getCharacterEncoding());
            assertTrue(response.getHeader("Content-disposition").contains("attachment"));
            // 验证Content-disposition头包含attachment，文件名可能经过编码
            String contentDisposition = response.getHeader("Content-disposition");
            assertTrue(contentDisposition != null && contentDisposition.contains("attachment"));

            // 验证方法调用
            verify(runFlowResultMapper, times(1)).selectRunFlowResultByFlowId(12345L);
            verify(runRuleMapper, times(1)).selectRunRuleDetailByFlowId(12345L);
            verify(fileComparisonService, times(1)).compareFileContents(any(FileComparisonRequestDto.class));
        }
    }

    @Test
    @DisplayName("测试导出HTML比对结果 - 带结果参数")
    void testExportHtmlComparisonResultWithResult_Success() throws ContrastBusinessException, IOException {
        // 准备测试数据
        HtmlComparisonResultDto result = new HtmlComparisonResultDto();
        result.setBaselineServer("基线服务器");
        result.setTargetServer("目标服务器");
        result.setTotalSourceFiles(10);
        result.setTotalTargetFiles(10);
        result.setConsistentCount(8);
        result.setInconsistentCount(1);
        result.setMissingCount(1);
        result.setExtraCount(0);
        result.setConsistentFiles(new ArrayList<>());
        result.setInconsistentFiles(new ArrayList<>());
        result.setMissingFiles(new ArrayList<>());
        result.setExtraFiles(new ArrayList<>());

        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行测试
        assertDoesNotThrow(() -> {
            htmlComparisonService.exportHtmlComparisonResult(request, result, response);
        });

        // 验证响应头
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",
                response.getContentType());
        assertEquals("utf-8", response.getCharacterEncoding());
        assertTrue(response.getHeader("Content-disposition").contains("attachment"));
    }

    @Test
    @DisplayName("测试导出HTML比对结果 - 导出异常")
    void testExportHtmlComparisonResult_ExportError() throws ContrastBusinessException {
        // 准备mock数据 - 模拟导出过程中的异常
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenReturn(fileComparisonResult);

        // 创建一个会抛出异常的response
        HttpServletResponse response = mock(HttpServletResponse.class);
        try {
            when(response.getOutputStream()).thenThrow(new IOException("输出流异常"));
        } catch (IOException e) {
            // 忽略
        }

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试并验证异常
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
                htmlComparisonService.exportHtmlComparisonResult(request, response);
            });

            assertTrue(exception.getMessage().contains("HTML比对结果导出失败"));
        }
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 文件比对服务异常")
    void testParseHtmlComparison_FileComparisonServiceError() throws ContrastBusinessException {
        // 准备mock数据
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("文件比对服务异常"));

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试并验证异常
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
                htmlComparisonService.parseHtmlComparison(request);
            });

            assertTrue(exception.getMessage().contains("HTML比对解析失败"));
        }
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 使用stderr内容")
    void testParseHtmlComparison_UseStderrContent() throws ContrastBusinessException {
        // 准备mock数据 - content为空，使用stderr
        runFlowResult.setContent("");
        runFlowResult.setStderr("{\"content\":\"stderr content\"}");
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenReturn(fileComparisonResult);

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            // 验证结果
            assertNotNull(result);
            verify(runFlowResultMapper, times(1)).selectRunFlowResultByFlowId(12345L);
        }
    }

    @ParameterizedTest
    @MethodSource("provideHtmlContentTestData")
    @DisplayName("测试HTML格式判断")
    void testIsHtmlFormat(String content, boolean expectedIsHtml) throws ContrastBusinessException {
        // 准备mock数据
        contentCustomDto.setContent(content);
        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));

        // 只有在实际需要时才mock文件比对服务
        if (!expectedIsHtml || content.equals("plain text content") || content.equals("file1.txt\nfile2.txt")) {
            when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                    .thenReturn(fileComparisonResult);
        }

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);
            assertNotNull(result);
        }
    }

    private static Stream<Object[]> provideHtmlContentTestData() {
        return Stream.of(
                new Object[]{"<div>test</div>", true},
                new Object[]{"<table><tr><td>test</td></tr></table>", true},
                new Object[]{"plain text content", false},
                new Object[]{"file1.txt\nfile2.txt", false},
                new Object[]{"  <div>  test  </div>  ", true}
        );
    }

    @Test
    @DisplayName("测试流程详情补充请求信息")
    void testEnrichRequestFromFlowDetail() throws ContrastBusinessException {
        // 准备测试数据 - 请求中缺少部分信息
        request.setBaselineServer(null);
        request.setTargetServer(null);
        request.setBaseServerIp(null);
        request.setTargetServerIp(null);
        request.setDescription(null);

        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenReturn(fileComparisonResult);

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            // 验证结果 - 信息应该从流程详情中补充
            assertNotNull(result);
            assertEquals("基线服务器", result.getBaselineServer());
            assertEquals("目标服务器", result.getTargetServer());
            assertEquals("***********", result.getBaseServerIp());
            assertEquals("***********", result.getTargetServerIp());
        }
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 多个流程详情")
    void testParseHtmlComparison_MultipleFlowDetails() throws ContrastBusinessException {
        // 准备多个流程详情
        RunFlowDetailBean detail2 = new RunFlowDetailBean();
        detail2.setSourceComputerName("源服务器2");
        detail2.setTargetComputerName("目标服务器2");
        detail2.setSourceComputerIp("***********");
        detail2.setTargetComputerIp("***********");

        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean, detail2));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenReturn(fileComparisonResult);

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            // 验证结果 - 应该使用第一个流程详情
            assertNotNull(result);
            assertEquals("基线服务器", result.getBaselineServer());
            assertEquals("目标服务器", result.getTargetServer());
        }
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 复杂HTML内容")
    void testParseHtmlComparison_ComplexHtmlContent() throws ContrastBusinessException {
        // 准备复杂的HTML内容
        String complexHtml = "<html><body><table>" +
                "<tr><td class=\"cp_frame cpi_td_w\"><div class=\"cp_text\">1</div><div class=\"cp_cn\">file1.txt</div></td>" +
                "<td class=\"cp_frame warning cpi_td_w\"><div class=\"cp_text\">1</div><div class=\"cp_cn\">file1.txt</div></td></tr>" +
                "</table></body></html>";

        contentCustomDto.setContent(complexHtml);

        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        // 移除不必要的mock，让实际解析逻辑决定是否需要调用文件比对服务

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.getTotalHtmlRows() >= 0);
        }
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 包含特殊字符的内容")
    void testParseHtmlComparison_SpecialCharacters() throws ContrastBusinessException {
        // 准备包含特殊字符的内容
        contentCustomDto.setContent("<div>测试&amp;特殊&lt;字符&gt;</div>");

        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        // 移除不必要的mock，让实际解析逻辑决定是否需要调用文件比对服务

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            // 验证结果
            assertNotNull(result);
        }
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 大量数据")
    void testParseHtmlComparison_LargeData() throws ContrastBusinessException {
        // 准备大量数据的文件比对结果
        FileComparisonResultDto largeResult = new FileComparisonResultDto();
        largeResult.setBaselineServer("基线服务器");
        largeResult.setTargetServer("目标服务器");
        largeResult.setTotalSourceFiles(1000);
        largeResult.setTotalTargetFiles(1000);
        largeResult.setConsistentCount(800);
        largeResult.setInconsistentCount(100);
        largeResult.setMissingCount(50);
        largeResult.setExtraCount(50);

        // 创建大量文件信息
        List<FileInfoDto> largeFileList = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            FileInfoDto file = new FileInfoDto();
            file.setFilePath("/test/file" + i + ".txt");
            file.setFileSize("1024");
            largeFileList.add(file);
        }
        largeResult.setConsistentFiles(largeFileList);
        largeResult.setInconsistentFiles(new ArrayList<>());
        largeResult.setMissingFiles(new ArrayList<>());
        largeResult.setExtraFiles(new ArrayList<>());

        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenReturn(largeResult);

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(1000, result.getTotalSourceFiles());
            assertEquals(1000, result.getTotalTargetFiles());
            assertEquals(800, result.getConsistentCount());
        }
    }

    @Test
    @DisplayName("测试导出HTML比对结果 - 空文件列表")
    void testExportHtmlComparisonResult_EmptyFileLists() throws ContrastBusinessException, IOException {
        // 准备空文件列表的结果
        fileComparisonResult.setConsistentFiles(Collections.emptyList());
        fileComparisonResult.setInconsistentFiles(Collections.emptyList());
        fileComparisonResult.setMissingFiles(Collections.emptyList());
        fileComparisonResult.setExtraFiles(Collections.emptyList());

        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenReturn(fileComparisonResult);

        MockHttpServletResponse response = new MockHttpServletResponse();

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            assertDoesNotThrow(() -> {
                htmlComparisonService.exportHtmlComparisonResult(request, response);
            });

            // 验证响应
            assertTrue(response.getContentAsByteArray().length > 0);
        }
    }

    @Test
    @DisplayName("测试导出HTML比对结果 - 文件名包含特殊字符")
    void testExportHtmlComparisonResult_SpecialCharactersInFilename() throws ContrastBusinessException, IOException {
        // 准备包含特殊字符的服务器名称
        runFlowDetailBean.setSourceComputerName("源服务器(测试)");
        runFlowDetailBean.setTargetComputerName("目标服务器[测试]");

        when(runFlowResultMapper.selectRunFlowResultByFlowId(anyLong())).thenReturn(runFlowResult);
        when(runRuleMapper.selectRunRuleDetailByFlowId(anyLong())).thenReturn(Arrays.asList(runFlowDetailBean));
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenReturn(fileComparisonResult);

        MockHttpServletResponse response = new MockHttpServletResponse();

        try (MockedStatic<JSON> mockedJSON = mockStatic(JSON.class)) {
            mockedJSON.when(() -> JSON.parseObject(anyString(), eq(ContentCustomDto.class)))
                    .thenReturn(contentCustomDto);

            // 执行测试
            assertDoesNotThrow(() -> {
                htmlComparisonService.exportHtmlComparisonResult(request, response);
            });

            // 验证文件名处理
            String contentDisposition = response.getHeader("Content-disposition");
            assertNotNull(contentDisposition);
            assertTrue(contentDisposition.contains("attachment"));
        }
    }

    @Test
    @DisplayName("测试构造函数")
    void testConstructor() {
        // 创建新的服务实例
        HtmlComparisonServiceImpl service = new HtmlComparisonServiceImpl(fileComparisonService, runFlowResultMapper, runRuleMapper);

        // 验证实例创建成功
        assertNotNull(service);
    }
}
