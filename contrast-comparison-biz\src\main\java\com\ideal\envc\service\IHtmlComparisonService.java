package com.ideal.envc.service;

import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;

import javax.servlet.http.HttpServletResponse;

/**
 * HTML比对服务接口
 *
 * <AUTHOR>
 */
public interface IHtmlComparisonService {

    /**
     * 解析HTML比对内容
     *
     * @param request 比对请求参数
     * @return 比对结果
     * @throws ContrastBusinessException 业务异常
     */
    HtmlComparisonResultDto parseHtmlComparison(HtmlComparisonRequestDto request) throws ContrastBusinessException;

    /**
     * 导出HTML比对结果到Excel
     *
     * @param request 比对请求参数
     * @param response HTTP响应对象
     * @throws ContrastBusinessException 业务异常
     */
    void exportHtmlComparisonResult(HtmlComparisonRequestDto request, HttpServletResponse response) throws ContrastBusinessException;

    /**
     * 导出HTML比对结果到Excel（带结果对象）
     *
     * @param request 比对请求参数
     * @param result 比对结果
     * @param response HTTP响应对象
     * @throws ContrastBusinessException 业务异常
     */
    void exportHtmlComparisonResult(HtmlComparisonRequestDto request, HtmlComparisonResultDto result, HttpServletResponse response) throws ContrastBusinessException;
}
