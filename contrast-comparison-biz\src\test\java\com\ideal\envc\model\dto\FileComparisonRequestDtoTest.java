package com.ideal.envc.model.dto;

import com.ideal.envc.model.enums.FileComparisonStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件比较请求DTO单元测试
 *
 * <AUTHOR>
 */
public class FileComparisonRequestDtoTest {

    private Validator validator;
    private FileComparisonRequestDto requestDto;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
        
        requestDto = new FileComparisonRequestDto();
    }

    @Test
    @DisplayName("测试有效的请求DTO")
    void testValidRequestDto() {
        // 设置有效数据
        requestDto.setSourceContent("source content");
        requestDto.setTargetContent("target content");
        requestDto.setBaselineServer("基线服务器");
        requestDto.setTargetServer("目标服务器");
        requestDto.setComparisonStrategy(FileComparisonStrategy.MD5_ONLY);

        // 验证
        Set<ConstraintViolation<FileComparisonRequestDto>> violations = validator.validate(requestDto);
        assertTrue(violations.isEmpty(), "有效的请求DTO不应该有验证错误");
    }

    @Test
    @DisplayName("测试源内容为空的验证")
    void testSourceContentEmpty() {
        // 设置空的源内容
        requestDto.setSourceContent("");
        requestDto.setTargetContent("target content");

        // 验证
        Set<ConstraintViolation<FileComparisonRequestDto>> violations = validator.validate(requestDto);
        assertFalse(violations.isEmpty(), "空的源内容应该有验证错误");
        
        boolean hasSourceContentError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("sourceContent"));
        assertTrue(hasSourceContentError, "应该有源内容的验证错误");
    }

    @Test
    @DisplayName("测试源内容为null的验证")
    void testSourceContentNull() {
        // 设置null的源内容
        requestDto.setSourceContent(null);
        requestDto.setTargetContent("target content");

        // 验证
        Set<ConstraintViolation<FileComparisonRequestDto>> violations = validator.validate(requestDto);
        assertFalse(violations.isEmpty(), "null的源内容应该有验证错误");
    }

    @Test
    @DisplayName("测试目标内容为空的验证")
    void testTargetContentEmpty() {
        // 设置空的目标内容
        requestDto.setSourceContent("source content");
        requestDto.setTargetContent("");

        // 验证
        Set<ConstraintViolation<FileComparisonRequestDto>> violations = validator.validate(requestDto);
        assertFalse(violations.isEmpty(), "空的目标内容应该有验证错误");
        
        boolean hasTargetContentError = violations.stream()
                .anyMatch(v -> v.getPropertyPath().toString().equals("targetContent"));
        assertTrue(hasTargetContentError, "应该有目标内容的验证错误");
    }

    @Test
    @DisplayName("测试目标内容为null的验证")
    void testTargetContentNull() {
        // 设置null的目标内容
        requestDto.setSourceContent("source content");
        requestDto.setTargetContent(null);

        // 验证
        Set<ConstraintViolation<FileComparisonRequestDto>> violations = validator.validate(requestDto);
        assertFalse(violations.isEmpty(), "null的目标内容应该有验证错误");
    }

    @Test
    @DisplayName("测试默认比较策略")
    void testDefaultComparisonStrategy() {
        // 新创建的DTO应该有默认的比较策略
        FileComparisonRequestDto newDto = new FileComparisonRequestDto();
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, newDto.getComparisonStrategy(),
                    "默认比较策略应该是COMPREHENSIVE");
    }

    @Test
    @DisplayName("测试设置比较策略")
    void testSetComparisonStrategy() {
        // 测试设置有效的比较策略
        requestDto.setComparisonStrategy(FileComparisonStrategy.COMPREHENSIVE);
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, requestDto.getComparisonStrategy());

        // 测试设置MD5_ONLY策略
        requestDto.setComparisonStrategy(FileComparisonStrategy.MD5_ONLY);
        assertEquals(FileComparisonStrategy.MD5_ONLY, requestDto.getComparisonStrategy());
    }

    @Test
    @DisplayName("测试设置null比较策略")
    void testSetNullComparisonStrategy() {
        // 设置null策略应该保持为null，默认值处理在服务层
        requestDto.setComparisonStrategy(null);
        assertNull(requestDto.getComparisonStrategy(),
                    "null策略应该保持为null，默认值处理在服务层");
    }

    @Test
    @DisplayName("测试所有属性的getter和setter")
    void testGettersAndSetters() {
        // 测试sourceContent
        String sourceContent = "test source content";
        requestDto.setSourceContent(sourceContent);
        assertEquals(sourceContent, requestDto.getSourceContent());

        // 测试targetContent
        String targetContent = "test target content";
        requestDto.setTargetContent(targetContent);
        assertEquals(targetContent, requestDto.getTargetContent());

        // 测试baselineServer
        String baselineServer = "基线服务器";
        requestDto.setBaselineServer(baselineServer);
        assertEquals(baselineServer, requestDto.getBaselineServer());

        // 测试targetServer
        String targetServer = "目标服务器";
        requestDto.setTargetServer(targetServer);
        assertEquals(targetServer, requestDto.getTargetServer());

        // 测试baseServerIp
        String baseServerIp = "*************";
        requestDto.setBaseServerIp(baseServerIp);
        assertEquals(baseServerIp, requestDto.getBaseServerIp());

        // 测试targetServerIp
        String targetServerIp = "*************";
        requestDto.setTargetServerIp(targetServerIp);
        assertEquals(targetServerIp, requestDto.getTargetServerIp());

        // 测试description
        String description = "测试描述";
        requestDto.setDescription(description);
        assertEquals(description, requestDto.getDescription());

        // 测试comparisonStrategy
        FileComparisonStrategy strategy = FileComparisonStrategy.COMPREHENSIVE;
        requestDto.setComparisonStrategy(strategy);
        assertEquals(strategy, requestDto.getComparisonStrategy());
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // 设置所有属性
        requestDto.setSourceContent("source content");
        requestDto.setTargetContent("target content");
        requestDto.setBaselineServer("基线服务器");
        requestDto.setTargetServer("目标服务器");
        requestDto.setBaseServerIp("*************");
        requestDto.setTargetServerIp("*************");
        requestDto.setDescription("测试描述");
        requestDto.setComparisonStrategy(FileComparisonStrategy.COMPREHENSIVE);

        String toStringResult = requestDto.toString();

        // 验证toString包含所有重要信息
        assertTrue(toStringResult.contains("sourceContent"));
        assertTrue(toStringResult.contains("targetContent"));
        assertTrue(toStringResult.contains("基线服务器"));
        assertTrue(toStringResult.contains("目标服务器"));
        assertTrue(toStringResult.contains("*************"));
        assertTrue(toStringResult.contains("*************"));
        assertTrue(toStringResult.contains("测试描述"));
        assertTrue(toStringResult.contains("COMPREHENSIVE"));
    }

    @Test
    @DisplayName("测试对象相等性")
    void testEquality() {
        // 创建两个相同的DTO
        FileComparisonRequestDto dto1 = new FileComparisonRequestDto();
        dto1.setSourceContent("source");
        dto1.setTargetContent("target");
        dto1.setComparisonStrategy(FileComparisonStrategy.MD5_ONLY);

        FileComparisonRequestDto dto2 = new FileComparisonRequestDto();
        dto2.setSourceContent("source");
        dto2.setTargetContent("target");
        dto2.setComparisonStrategy(FileComparisonStrategy.MD5_ONLY);

        // 测试自反性
        assertEquals(dto1, dto1);

        // 注意：由于没有重写equals方法，这里只是测试引用相等性
        assertNotSame(dto1, dto2);
    }

    @Test
    @DisplayName("测试序列化兼容性")
    void testSerializationCompatibility() {
        // 测试DTO是否实现了Serializable接口
        assertTrue(requestDto instanceof java.io.Serializable, 
                  "FileComparisonRequestDto应该实现Serializable接口");
    }
}
