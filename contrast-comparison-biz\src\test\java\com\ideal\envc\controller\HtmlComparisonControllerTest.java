package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IHtmlComparisonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * HTML比对控制器测试类
 * 测试统一响应码的使用
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class HtmlComparisonControllerTest {

    @Mock
    private IHtmlComparisonService htmlComparisonService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private HtmlComparisonController htmlComparisonController;

    private UserDto userDto;
    private HtmlComparisonRequestDto request;
    private HtmlComparisonResultDto result;

    @BeforeEach
    void setUp() {
        // 初始化用户信息
        userDto = new UserDto();
        userDto.setFullName("测试用户");
        userDto.setId(1L);

        // 初始化请求参数
        request = new HtmlComparisonRequestDto();
        request.setFlowId(12345L);
        request.setBaselineServer("基线服务器");
        request.setTargetServer("目标服务器");
        request.setDescription("测试比对");

        // 初始化比对结果
        result = new HtmlComparisonResultDto();
        result.setTotalHtmlRows(100);
        result.setTotalSourceFiles(50);
        result.setTotalTargetFiles(48);
        result.setConsistentCount(45);
        result.setInconsistentCount(3);
        result.setMissingCount(2);
        result.setExtraCount(0);
        result.setConsistentRate(new BigDecimal(90));
        result.setInconsistentRate(new BigDecimal(6));
        result.setMissingRate(new BigDecimal(4));
        result.setExtraRate(new BigDecimal(0));
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 成功场景")
    void testParseHtmlComparison_Success() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        // 执行测试
        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), response.getMessage());
        assertNotNull(response.getData());
        assertEquals(result, response.getData());

        // 验证方法调用
        verify(userinfoComponent, times(1)).getUser();
        verify(htmlComparisonService, times(1)).parseHtmlComparison(any(HtmlComparisonRequestDto.class));
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 流程ID为空异常")
    void testParseHtmlComparison_FlowIdEmpty() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("流程ID不能为空"));

        // 执行测试
        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.FLOW_ID_EMPTY.getCode(), response.getCode());
        assertEquals("流程ID不能为空", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 流程结果不存在异常")
    void testParseHtmlComparison_FlowResultNotFound() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("未查询到对应的流程结果数据"));

        // 执行测试
        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode(), response.getCode());
        assertEquals("未查询到对应的流程结果数据", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 系统异常")
    void testParseHtmlComparison_SystemError() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new RuntimeException("系统内部错误"));

        // 执行测试
        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试快速解析HTML比对内容 - 成功场景")
    void testQuickParseHtmlComparison_Success() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        // 执行测试
        R<String> response = htmlComparisonController.quickParseHtmlComparison(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData().contains("HTML比对解析完成"));
        assertTrue(response.getData().contains("基线文件：50个"));
        assertTrue(response.getData().contains("一致：45个（90%）"));
    }

    @Test
    @DisplayName("测试获取比对建议 - 成功场景")
    void testGetComparisonAdvice_Success() throws Exception {
        // 准备mock
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        // 执行测试
        R<String> response = htmlComparisonController.getComparisonAdvice(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), response.getMessage());
        assertNotNull(response.getData());
        assertTrue(response.getData().contains("差异率"));
        assertTrue(response.getData().contains("详细统计"));
    }

    @Test
    @DisplayName("测试导出HTML比对结果 - 成功场景")
    void testExportHtmlComparison_Success() throws Exception {
        // 准备mock
        MockHttpServletResponse response = new MockHttpServletResponse();
        Long flowId = 12345L;
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doNothing().when(htmlComparisonService).exportHtmlComparisonResult(any(), any());

        // 执行测试 - 不应该抛出异常
        assertDoesNotThrow(() -> {
            htmlComparisonController.exportHtmlComparison(flowId, response);
        });

        // 验证方法调用
        verify(userinfoComponent, times(1)).getUser();
        verify(htmlComparisonService, times(1)).exportHtmlComparisonResult(any(), any());
    }

    @Test
    @DisplayName("测试导出HTML比对结果 - 业务异常")
    void testExportHtmlComparison_BusinessException() throws Exception {
        // 准备mock
        MockHttpServletResponse response = new MockHttpServletResponse();
        Long flowId = 12345L;
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doThrow(new ContrastBusinessException("Excel导出失败"))
                .when(htmlComparisonService).exportHtmlComparisonResult(any(), any());

        // 执行测试
        htmlComparisonController.exportHtmlComparison(flowId, response);

        // 验证响应状态
        assertEquals(500, response.getStatus());
        assertEquals("application/json;charset=utf-8", response.getContentType());

        String responseContent = response.getContentAsString();
        assertTrue(responseContent.contains(ResponseCodeEnum.EXCEL_EXPORT_FAIL.getCode()));
        assertTrue(responseContent.contains("Excel导出失败"));
    }

    @Test
    @DisplayName("测试统一响应码枚举的完整性")
    void testResponseCodeEnumCompleteness() {
        // 验证比对解析相关响应码
        assertNotNull(ResponseCodeEnum.COMPARISON_PARSE_FAIL);
        assertEquals("130600", ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode());
        assertEquals("比对解析失败", ResponseCodeEnum.COMPARISON_PARSE_FAIL.getDesc());

        assertNotNull(ResponseCodeEnum.FLOW_ID_EMPTY);
        assertEquals("130602", ResponseCodeEnum.FLOW_ID_EMPTY.getCode());
        assertEquals("流程ID不能为空", ResponseCodeEnum.FLOW_ID_EMPTY.getDesc());

        assertNotNull(ResponseCodeEnum.FLOW_RESULT_NOT_FOUND);
        assertEquals("130603", ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode());
        assertEquals("流程结果不存在", ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getDesc());

        // 验证导出相关响应码
        assertNotNull(ResponseCodeEnum.EXPORT_FAIL);
        assertEquals("130700", ResponseCodeEnum.EXPORT_FAIL.getCode());
        assertEquals("导出失败", ResponseCodeEnum.EXPORT_FAIL.getDesc());

        assertNotNull(ResponseCodeEnum.EXCEL_EXPORT_FAIL);
        assertEquals("130701", ResponseCodeEnum.EXCEL_EXPORT_FAIL.getCode());
        assertEquals("Excel导出失败", ResponseCodeEnum.EXCEL_EXPORT_FAIL.getDesc());
    }

    @Test
    @DisplayName("测试业务异常响应方法的完整性")
    void testBusinessExceptionResponseCompleteness() throws Exception {
        // 测试各种业务异常的响应码映射
        String[] testMessages = {
                "流程ID不能为空",
                "未查询到对应的流程结果数据",
                "未查询到对应设备相关信息",
                "流程结果内容为空",
                "HTML内容为空",
                "内容格式不正确",
                "HTML解析失败",
                "JSON解析失败",
                "其他解析失败"
        };

        String[] expectedCodes = {
                ResponseCodeEnum.FLOW_ID_EMPTY.getCode(),
                ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode(),
                ResponseCodeEnum.FLOW_DETAIL_NOT_FOUND.getCode(),
                ResponseCodeEnum.FLOW_CONTENT_EMPTY.getCode(),
                ResponseCodeEnum.HTML_CONTENT_EMPTY.getCode(),
                ResponseCodeEnum.CONTENT_PARSE_FAIL.getCode(),
                ResponseCodeEnum.HTML_PARSE_FAIL.getCode(),
                ResponseCodeEnum.JSON_PARSE_FAIL.getCode(),
                ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode()
        };

        for (int i = 0; i < testMessages.length; i++) {
            when(userinfoComponent.getUser()).thenReturn(userDto);
            when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                    .thenThrow(new ContrastBusinessException(testMessages[i]));

            R<String> response = htmlComparisonController.quickParseHtmlComparison(request);

            assertEquals(expectedCodes[i], response.getCode(),
                        "消息 '" + testMessages[i] + "' 应该映射到响应码 " + expectedCodes[i]);
            assertEquals(testMessages[i], response.getMessage());
        }
    }

    @Test
    @DisplayName("测试导出错误处理方法的完整性")
    void testExportErrorHandlingCompleteness() throws Exception {
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 测试各种导出异常的响应码映射
        String[] testMessages = {
                "流程ID不能为空",
                "流程结果不存在",
                "流程详情不存在",
                "流程内容为空",
                "导出数据为空",
                "Excel导出失败",
                "文件写入失败",
                "权限不足",
                "系统异常"
        };

        String[] expectedCodes = {
                ResponseCodeEnum.FLOW_ID_EMPTY.getCode(),
                ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode(),
                ResponseCodeEnum.FLOW_DETAIL_NOT_FOUND.getCode(),
                ResponseCodeEnum.FLOW_CONTENT_EMPTY.getCode(),
                ResponseCodeEnum.EXPORT_DATA_EMPTY.getCode(),
                ResponseCodeEnum.EXCEL_EXPORT_FAIL.getCode(),
                ResponseCodeEnum.FILE_WRITE_FAIL.getCode(),
                ResponseCodeEnum.EXPORT_PERMISSION_DENIED.getCode(),
                ResponseCodeEnum.SYSTEM_ERROR.getCode()
        };

        Long flowId = 12345L;
        for (int i = 0; i < testMessages.length; i++) {
            when(userinfoComponent.getUser()).thenReturn(userDto);
            doThrow(new ContrastBusinessException(testMessages[i]))
                    .when(htmlComparisonService).exportHtmlComparisonResult(any(), any());

            // 重置response
            response = new MockHttpServletResponse();

            htmlComparisonController.exportHtmlComparison(flowId, response);

            // 验证响应状态和内容
            assertEquals(500, response.getStatus());
            assertEquals("application/json;charset=utf-8", response.getContentType());

            String responseContent = response.getContentAsString();
            assertTrue(responseContent.contains(expectedCodes[i]),
                      "消息 '" + testMessages[i] + "' 应该映射到响应码 " + expectedCodes[i]);
            assertTrue(responseContent.contains(testMessages[i]));
        }
    }

    @Test
    @DisplayName("测试导出系统异常处理")
    void testExportSystemException() throws Exception {
        MockHttpServletResponse response = new MockHttpServletResponse();
        Long flowId = 12345L;

        when(userinfoComponent.getUser()).thenReturn(userDto);
        doThrow(new RuntimeException("系统内部错误"))
                .when(htmlComparisonService).exportHtmlComparisonResult(any(), any());

        htmlComparisonController.exportHtmlComparison(flowId, response);

        // 验证响应状态和内容
        assertEquals(500, response.getStatus());
        assertEquals("application/json;charset=utf-8", response.getContentType());

        String responseContent = response.getContentAsString();
        assertTrue(responseContent.contains(ResponseCodeEnum.SYSTEM_ERROR.getCode()));
        assertTrue(responseContent.contains(ResponseCodeEnum.SYSTEM_ERROR.getDesc()));
    }

    @Test
    @DisplayName("测试比对建议生成 - 高差异率场景")
    void testGetComparisonAdvice_HighDifferenceRate() throws Exception {
        // 准备高差异率的结果数据
        result.setTotalSourceFiles(100);
        result.setInconsistentCount(30);
        result.setMissingCount(25);
        result.setConsistentCount(45);
        result.setExtraCount(5);

        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        R<String> response = htmlComparisonController.getComparisonAdvice(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertTrue(response.getData().contains("🚨 差异率超过50%"));
        assertTrue(response.getData().contains("全面检查环境配置"));
    }

    @Test
    @DisplayName("测试比对建议生成 - 中等差异率场景")
    void testGetComparisonAdvice_MediumDifferenceRate() throws Exception {
        // 准备中等差异率的结果数据
        result.setTotalSourceFiles(100);
        result.setInconsistentCount(15);
        result.setMissingCount(10);
        result.setConsistentCount(75);
        result.setExtraCount(0);

        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        R<String> response = htmlComparisonController.getComparisonAdvice(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertTrue(response.getData().contains("⚠️ 差异率在20-50%之间"));
        assertTrue(response.getData().contains("重点关注关键文件"));
    }

    @Test
    @DisplayName("测试比对建议生成 - 低差异率场景")
    void testGetComparisonAdvice_LowDifferenceRate() throws Exception {
        // 准备低差异率的结果数据
        result.setTotalSourceFiles(100);
        result.setInconsistentCount(2);
        result.setMissingCount(1);
        result.setConsistentCount(97);
        result.setExtraCount(0);

        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        R<String> response = htmlComparisonController.getComparisonAdvice(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertTrue(response.getData().contains("✅ 差异率低于5%"));
        assertTrue(response.getData().contains("环境高度一致"));
    }

    @Test
    @DisplayName("测试比对建议生成 - 无文件场景")
    void testGetComparisonAdvice_NoFiles() throws Exception {
        // 准备无文件的结果数据
        result.setTotalSourceFiles(0);
        result.setInconsistentCount(0);
        result.setMissingCount(0);
        result.setConsistentCount(0);
        result.setExtraCount(0);

        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        R<String> response = htmlComparisonController.getComparisonAdvice(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertTrue(response.getData().contains("无文件需要比对"));
    }

    @Test
    @DisplayName("测试比对建议生成 - 空结果场景")
    void testGetComparisonAdvice_NullResult() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(null);

        R<String> response = htmlComparisonController.getComparisonAdvice(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertTrue(response.getData().contains("无法提供建议：比对结果为空"));
    }

    @Test
    @DisplayName("测试快速解析HTML比对内容 - 业务异常")
    void testQuickParseHtmlComparison_BusinessException() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("解析失败"));

        R<String> response = htmlComparisonController.quickParseHtmlComparison(request);

        assertNotNull(response);
        // 根据实际的响应码映射逻辑调整期望值
        assertTrue(response.getCode().startsWith("1306")); // 比对相关错误码
        assertEquals("解析失败", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试快速解析HTML比对内容 - 系统异常")
    void testQuickParseHtmlComparison_SystemException() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new RuntimeException("系统异常"));

        R<String> response = htmlComparisonController.quickParseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试获取比对建议 - 业务异常")
    void testGetComparisonAdvice_BusinessException() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("获取建议失败"));

        R<String> response = htmlComparisonController.getComparisonAdvice(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode(), response.getCode());
        assertEquals("获取建议失败", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试获取比对建议 - 系统异常")
    void testGetComparisonAdvice_SystemException() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new RuntimeException("系统异常"));

        R<String> response = htmlComparisonController.getComparisonAdvice(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), response.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 其他业务异常")
    void testParseHtmlComparison_OtherBusinessException() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("其他业务异常"));

        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode(), response.getCode());
        assertEquals("其他业务异常", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 流程详情不存在异常")
    void testParseHtmlComparison_FlowDetailNotFound() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("未查询到对应设备相关信息"));

        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.FLOW_DETAIL_NOT_FOUND.getCode(), response.getCode());
        assertEquals("未查询到对应设备相关信息", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 流程内容为空异常")
    void testParseHtmlComparison_FlowContentEmpty() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("流程结果内容为空"));

        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.FLOW_CONTENT_EMPTY.getCode(), response.getCode());
        assertEquals("流程结果内容为空", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - HTML内容为空异常")
    void testParseHtmlComparison_HtmlContentEmpty() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("HTML内容为空"));

        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.HTML_CONTENT_EMPTY.getCode(), response.getCode());
        assertEquals("HTML内容为空", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - 内容解析失败异常")
    void testParseHtmlComparison_ContentParseFail() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("内容格式不正确"));

        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.CONTENT_PARSE_FAIL.getCode(), response.getCode());
        assertEquals("内容格式不正确", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - HTML解析失败异常")
    void testParseHtmlComparison_HtmlParseFail() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("HTML解析失败"));

        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.HTML_PARSE_FAIL.getCode(), response.getCode());
        assertEquals("HTML解析失败", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试解析HTML比对内容 - JSON解析失败异常")
    void testParseHtmlComparison_JsonParseFail() throws Exception {
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("JSON解析失败"));

        R<HtmlComparisonResultDto> response = htmlComparisonController.parseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.JSON_PARSE_FAIL.getCode(), response.getCode());
        assertEquals("JSON解析失败", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试快速解析HTML比对内容 - 包含详细统计信息")
    void testQuickParseHtmlComparison_DetailedStats() throws Exception {
        // 准备详细的结果数据
        result.setTotalHtmlRows(500);
        result.setTotalSourceFiles(200);
        result.setTotalTargetFiles(195);
        result.setConsistentCount(180);
        result.setInconsistentCount(10);
        result.setMissingCount(5);
        result.setExtraCount(0);
        result.setConsistentRate(new BigDecimal("90.00"));
        result.setInconsistentRate(new BigDecimal("5.00"));
        result.setMissingRate(new BigDecimal("2.50"));
        result.setExtraRate(new BigDecimal("0.00"));

        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        R<String> response = htmlComparisonController.quickParseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertNotNull(response.getData());

        String data = response.getData();
        assertTrue(data.contains("HTML比对解析完成"));
        assertTrue(data.contains("HTML行数：500行"));
        assertTrue(data.contains("基线文件：200个"));
        assertTrue(data.contains("目标文件：195个"));
        assertTrue(data.contains("一致：180个（90.00%）"));
        assertTrue(data.contains("不一致：10个（5.00%）"));
        assertTrue(data.contains("缺失：5个（2.50%）"));
        assertTrue(data.contains("多出：0个（0.00%）"));
    }

    @Test
    @DisplayName("测试快速解析HTML比对内容 - 零文件场景")
    void testQuickParseHtmlComparison_ZeroFiles() throws Exception {
        // 准备零文件的结果数据
        result.setTotalHtmlRows(0);
        result.setTotalSourceFiles(0);
        result.setTotalTargetFiles(0);
        result.setConsistentCount(0);
        result.setInconsistentCount(0);
        result.setMissingCount(0);
        result.setExtraCount(0);
        result.setConsistentRate(new BigDecimal("0.00"));
        result.setInconsistentRate(new BigDecimal("0.00"));
        result.setMissingRate(new BigDecimal("0.00"));
        result.setExtraRate(new BigDecimal("0.00"));

        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class)))
                .thenReturn(result);

        R<String> response = htmlComparisonController.quickParseHtmlComparison(request);

        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertNotNull(response.getData());

        String data = response.getData();
        assertTrue(data.contains("HTML比对解析完成"));
        assertTrue(data.contains("HTML行数：0行"));
        assertTrue(data.contains("基线文件：0个"));
        assertTrue(data.contains("目标文件：0个"));
    }

    @Test
    @DisplayName("测试构造函数")
    void testConstructor() {
        // 创建新的控制器实例
        HtmlComparisonController controller = new HtmlComparisonController(htmlComparisonService, userinfoComponent);

        // 验证实例创建成功
        assertNotNull(controller);
    }
}
