package com.ideal.envc.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.common.ContrastToolUtils;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.ResultMonitorMapper;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.bean.ResultMonitorBean;
import com.ideal.envc.model.bean.RunFlowDetailBean;
import com.ideal.envc.model.ContentDetailDto;
import com.ideal.envc.model.dto.ResultMonitorDto;
import com.ideal.envc.model.dto.ResultMonitorQueryDto;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.enums.RuleModelEnum;
import com.ideal.envc.model.enums.StartFromEnums;
import com.ideal.envc.component.FileComparisonComponent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ResultMonitorServiceImpl的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class ResultMonitorServiceImplTest {

    @Mock
    private ResultMonitorMapper resultMonitorMapper;
    
    @Mock
    private RunFlowResultMapper runFlowResultMapper;
    
    @Mock
    private RunFlowMapper runFlowMapper;

    @Mock
    private RunRuleMapper runRuleMapper;

    @Mock
    private FileComparisonComponent fileComparisonComponent;

    @InjectMocks
    private ResultMonitorServiceImpl resultMonitorService;

    private ResultMonitorQueryDto queryDto;
    private ResultMonitorBean monitorBean;
    private RunFlowResultEntity flowResult;
    private RunFlowEntity runFlow;
    private RunRuleEntity runRule;
    private RunFlowDetailBean runFlowDetailBean;

    @BeforeEach
    void setUp() {
        // 初始化查询DTO
        queryDto = new ResultMonitorQueryDto();
        queryDto.setBusinessSystemName("testSystem");
        queryDto.setModel(RuleModelEnum.COMPARE.getCode());
        queryDto.setResult(1);
        queryDto.setFrom(StartFromEnums.MANUAL_TRIGGER.getCode());

        // 初始化监控Bean
        monitorBean = new ResultMonitorBean();
        monitorBean.setId(1L);
        monitorBean.setRunRuleId(100L); // 新增runRuleId设置
        monitorBean.setCreateTime(new Timestamp(System.currentTimeMillis() - 1000));
        monitorBean.setElapsedTime(1000L);
        monitorBean.setFrom(StartFromEnums.MANUAL_TRIGGER.getCode());

        // 初始化流程结果实体
        flowResult = new RunFlowResultEntity();
        flowResult.setContent("source@$@target");
        flowResult.setStderr("error message");

        // 初始化流程实体
        runFlow = new RunFlowEntity();
        runFlow.setRunBizId(1L);

        // 初始化规则实体
        runRule = new RunRuleEntity();
        runRule.setSourcePath("/source/path");
        runRule.setPath("/target/path");

        // 初始化流程详情Bean
        runFlowDetailBean = new RunFlowDetailBean();
        runFlowDetailBean.setSourcePath("/source/path");
        runFlowDetailBean.setPath("/target/path");
        runFlowDetailBean.setBusinessSystemId(1L);
        runFlowDetailBean.setSourceComputerId(1L);
        runFlowDetailBean.setTargetComputerId(2L);
        runFlowDetailBean.setSourceCenterName("源中心");
        runFlowDetailBean.setTargetCenterName("目标中心");
        runFlowDetailBean.setSourceComputerIp("*************");
        runFlowDetailBean.setTargetComputerIp("*************");
        runFlowDetailBean.setSourceComputerName("源服务器");
        runFlowDetailBean.setTargetComputerName("目标服务器");
        runFlowDetailBean.setBusinessSystemName("测试系统");
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 正常场景")
    void testSelectResultMonitorList_Normal() {
        // 准备测试数据
        Page<ResultMonitorBean> page = new Page<>(1, 10);
        page.add(monitorBean);
        page.setTotal(1);
        doReturn(page).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), anyInt(), anyInt(), anyInt());

        // Mock ContrastToolUtils.formatElapsedTime静态方法
        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            mockedContrastToolUtils.when(() -> ContrastToolUtils.formatElapsedTime(1000L))
                    .thenReturn("1秒");

            // 执行测试
            PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());

            // 验证DTO中的新增字段
            ResultMonitorDto dto = result.getList().get(0);
            assertEquals("1秒", dto.getElapsedTimeStr());
            assertEquals("手动触发", dto.getTriggerFrom());

            verify(resultMonitorMapper).selectResultMonitorList(
                queryDto.getBusinessSystemName(),
                queryDto.getModel(),
                queryDto.getResult(),
                queryDto.getFrom()
            );

            // 验证ContrastToolUtils.formatElapsedTime被调用
            mockedContrastToolUtils.verify(() -> ContrastToolUtils.formatElapsedTime(1000L));
        }
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 查询条件为空")
    void testSelectResultMonitorList_NullQuery() {
        // 执行测试
        PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(null, 1, 10);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        verify(resultMonitorMapper, never()).selectResultMonitorList(any(), any(), any(), any());
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 模型为空")
    void testSelectResultMonitorList_NullModel() {
        // 准备测试数据
        queryDto.setModel(null);
        Page<ResultMonitorBean> page = new Page<>(1, 10);
        page.add(monitorBean);
        page.setTotal(1);
        doReturn(page).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), eq(RuleModelEnum.COMPARE.getCode()), anyInt(), anyInt());

        // Mock ContrastToolUtils.formatElapsedTime静态方法
        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            mockedContrastToolUtils.when(() -> ContrastToolUtils.formatElapsedTime(1000L))
                    .thenReturn("1秒");

            // 执行测试
            PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            assertEquals(RuleModelEnum.COMPARE.getCode(), queryDto.getModel());

            // 验证DTO中的新增字段
            ResultMonitorDto dto = result.getList().get(0);
            assertEquals("1秒", dto.getElapsedTimeStr());
            assertEquals("手动触发", dto.getTriggerFrom());
        }
    }

    @Test
    @DisplayName("测试查询比对结果列表 - from为空")
    void testSelectResultMonitorList_NullFrom() {
        // 准备测试数据
        queryDto.setFrom(null);
        Page<ResultMonitorBean> page = new Page<>(1, 10);
        page.add(monitorBean);
        page.setTotal(1);
        doReturn(page).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), anyInt(), anyInt(), isNull());

        // Mock ContrastToolUtils.formatElapsedTime静态方法
        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            mockedContrastToolUtils.when(() -> ContrastToolUtils.formatElapsedTime(1000L))
                    .thenReturn("1秒");

            // 执行测试
            PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());

            // 验证DTO中的新增字段
            ResultMonitorDto dto = result.getList().get(0);
            assertEquals("1秒", dto.getElapsedTimeStr());
            assertEquals("手动触发", dto.getTriggerFrom());

            verify(resultMonitorMapper).selectResultMonitorList(
                queryDto.getBusinessSystemName(),
                queryDto.getModel(),
                queryDto.getResult(),
                null
            );
        }
    }

    @Test
    @DisplayName("测试根据流程ID查询比对详情 - 正常场景")
    void testSelectContentDetailByFlowId_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        // 执行测试
        ContentDetailDto result = resultMonitorService.selectContentDetailByFlowId(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals("source", result.getSourceContent());
        assertEquals("target", result.getTargetContent());
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
    }

    @Test
    @DisplayName("测试根据流程ID查询比对详情 - 流程ID为空")
    void testSelectContentDetailByFlowId_NullFlowId() {
        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            resultMonitorService.selectContentDetailByFlowId(null));
    }

    @Test
    @DisplayName("测试根据流程ID查询比对详情 - 未找到结果")
    void testSelectContentDetailByFlowId_NoResult() {
        // 准备测试数据
        doReturn(null).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            resultMonitorService.selectContentDetailByFlowId(1L));
    }

    @Test
    @DisplayName("测试根据流程ID查询比对详情 - 使用stderr内容")
    void testSelectContentDetailByFlowId_UseStderr() throws ContrastBusinessException {
        // 准备测试数据
        flowResult.setContent(null);
        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        // 执行测试
        ContentDetailDto result = resultMonitorService.selectContentDetailByFlowId(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(flowResult.getStderr(), result.getSourceContent());
        assertEquals("", result.getTargetContent());
    }

    @Test
    @DisplayName("测试查询文件比对内容 - 正常场景")
    void testSelectContentForCompareFileByFlowId_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());
        doReturn(runFlow).when(runFlowMapper).selectRunFlowByFlowId(anyLong());
        doReturn(runRule).when(runRuleMapper).selectRunRuleById(anyLong());

        // 执行测试
        String result = resultMonitorService.selectContentForCompareFileByFlowId(1L);

        // 验证结果
        assertNotNull(result);
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
        verify(runFlowMapper).selectRunFlowByFlowId(1L);
        verify(runRuleMapper).selectRunRuleById(1L);
    }

    @Test
    @DisplayName("测试查询文件比对内容 - 流程ID为空")
    void testSelectContentForCompareFileByFlowId_NullFlowId() {
        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            resultMonitorService.selectContentForCompareFileByFlowId(null));
    }

    @Test
    @DisplayName("测试查询文件比对内容 - 未找到结果")
    void testSelectContentForCompareFileByFlowId_NoResult() {
        // 准备测试数据
        doReturn(null).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.selectContentForCompareFileByFlowId(1L)
        );
        
        assertEquals("未查询到对应的流程结果数据", exception.getMessage());
    }

    @Test
    @DisplayName("测试导出比对报表 - 正常场景JSON格式")
    void testExportComparisonReportByFlowId_Normal_JsonFormat() throws ContrastBusinessException {
        // 准备测试数据
        String jsonContent = "{\"sourceContent\":\"source file content\",\"targetContent\":\"target file content\",\"content\":\"comparison result\",\"ret\":true}";
        flowResult.setContent(jsonContent);

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());
        doReturn(Arrays.asList(runFlowDetailBean)).when(runRuleMapper).selectRunRuleDetailByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试
        resultMonitorService.exportComparisonReportByFlowId(1L, mockResponse);

        // 验证结果
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
        verify(runRuleMapper).selectRunRuleDetailByFlowId(1L);
        verify(fileComparisonComponent).compareAndExport(
                any(),
                eq(mockResponse)
        );
    }

    @Test
    @DisplayName("测试导出比对报表 - 正常场景分隔符格式")
    void testExportComparisonReportByFlowId_Normal_SeparatorFormat() throws ContrastBusinessException {
        // 准备测试数据
        String separatorContent = "source content@$@target content";
        flowResult.setContent(separatorContent);

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());
        doReturn(Arrays.asList(runFlowDetailBean)).when(runRuleMapper).selectRunRuleDetailByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试
        resultMonitorService.exportComparisonReportByFlowId(1L, mockResponse);

        // 验证结果
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
        verify(runRuleMapper).selectRunRuleDetailByFlowId(1L);
        verify(fileComparisonComponent).compareAndExport(
                any(),
                eq(mockResponse)
        );
    }

    @Test
    @DisplayName("测试导出比对报表 - 流程ID为空")
    void testExportComparisonReportByFlowId_NullFlowId() {
        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.exportComparisonReportByFlowId(null, mockResponse)
        );

        assertEquals("flowId不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试导出比对报表 - 未找到结果")
    void testExportComparisonReportByFlowId_NoResult() {
        // 准备测试数据
        doReturn(null).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.exportComparisonReportByFlowId(1L, mockResponse)
        );

        assertEquals("未查询到对应的流程结果数据", exception.getMessage());
    }

    @Test
    @DisplayName("测试导出比对报表 - 内容为空")
    void testExportComparisonReportByFlowId_EmptyContent() {
        // 准备测试数据
        flowResult.setContent(null);
        flowResult.setStderr(null);

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());
        doReturn(Arrays.asList(runFlowDetailBean)).when(runRuleMapper).selectRunRuleDetailByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.exportComparisonReportByFlowId(1L, mockResponse)
        );

        assertEquals("流程结果内容为空，无法导出比对报表", exception.getMessage());
    }

    @Test
    @DisplayName("测试导出比对报表 - 使用stderr内容")
    void testExportComparisonReportByFlowId_UseStderrContent() throws ContrastBusinessException {
        // 准备测试数据
        flowResult.setContent(null);
        flowResult.setStderr("stderr content only");

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());
        doReturn(Arrays.asList(runFlowDetailBean)).when(runRuleMapper).selectRunRuleDetailByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试
        resultMonitorService.exportComparisonReportByFlowId(1L, mockResponse);

        // 验证结果
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
        verify(runRuleMapper).selectRunRuleDetailByFlowId(1L);
        verify(fileComparisonComponent).compareAndExport(
                any(),
                eq(mockResponse)
        );
    }

    @Test
    @DisplayName("测试导出比对报表 - 未找到设备信息")
    void testExportComparisonReportByFlowId_NoDeviceInfo() {
        // 准备测试数据
        String jsonContent = "{\"sourceContent\":\"source content\",\"targetContent\":\"target content\"}";
        flowResult.setContent(jsonContent);

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());
        doReturn(new ArrayList<>()).when(runRuleMapper).selectRunRuleDetailByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.exportComparisonReportByFlowId(1L, mockResponse)
        );

        assertEquals("根据流程ID未查询到对应设备相关信息", exception.getMessage());
    }

    @Test
    @DisplayName("测试导出比对报表 - 解析后内容为空")
    void testExportComparisonReportByFlowId_ParsedContentEmpty() {
        // 准备测试数据 - 解析后内容为空
        String jsonContent = "{\"sourceContent\":\"\",\"targetContent\":\"\"}";
        flowResult.setContent(jsonContent);

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());
        doReturn(Arrays.asList(runFlowDetailBean)).when(runRuleMapper).selectRunRuleDetailByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.exportComparisonReportByFlowId(1L, mockResponse)
        );

        assertEquals("解析后的比对内容为空，无法导出比对报表", exception.getMessage());
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 耗时为null时动态计算")
    void testSelectResultMonitorList_ElapsedTimeNull() {
        // 准备测试数据 - 耗时为null
        ResultMonitorBean beanWithNullElapsedTime = new ResultMonitorBean();
        beanWithNullElapsedTime.setId(1L);
        beanWithNullElapsedTime.setRunRuleId(100L);
        beanWithNullElapsedTime.setCreateTime(new Timestamp(System.currentTimeMillis() - 5000)); // 5秒前
        beanWithNullElapsedTime.setElapsedTime(null); // 耗时为null
        beanWithNullElapsedTime.setFrom(StartFromEnums.MANUAL_TRIGGER.getCode());

        Page<ResultMonitorBean> page = new Page<>(1, 10);
        page.add(beanWithNullElapsedTime);
        page.setTotal(1);
        doReturn(page).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), anyInt(), anyInt(), anyInt());

        // Mock ContrastToolUtils.formatElapsedTime静态方法
        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            mockedContrastToolUtils.when(() -> ContrastToolUtils.formatElapsedTime(anyLong()))
                    .thenReturn("5秒");

            // 执行测试
            PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());

            // 验证DTO中的新增字段
            ResultMonitorDto dto = result.getList().get(0);
            assertEquals("5秒", dto.getElapsedTimeStr());
            assertEquals("手动触发", dto.getTriggerFrom());

            // 验证ContrastToolUtils.formatElapsedTime被调用，且参数大于0（动态计算的耗时）
            mockedContrastToolUtils.verify(() -> ContrastToolUtils.formatElapsedTime(longThat(time -> time > 0)));
        }
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 耗时为0时动态计算")
    void testSelectResultMonitorList_ElapsedTimeZero() {
        // 准备测试数据 - 耗时为0
        ResultMonitorBean beanWithZeroElapsedTime = new ResultMonitorBean();
        beanWithZeroElapsedTime.setId(1L);
        beanWithZeroElapsedTime.setRunRuleId(100L);
        beanWithZeroElapsedTime.setCreateTime(new Timestamp(System.currentTimeMillis() - 3000)); // 3秒前
        beanWithZeroElapsedTime.setElapsedTime(0L); // 耗时为0
        beanWithZeroElapsedTime.setFrom(StartFromEnums.PERIOD_TRIGGER.getCode());

        Page<ResultMonitorBean> page = new Page<>(1, 10);
        page.add(beanWithZeroElapsedTime);
        page.setTotal(1);
        doReturn(page).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), anyInt(), anyInt(), anyInt());

        // Mock ContrastToolUtils.formatElapsedTime静态方法
        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            mockedContrastToolUtils.when(() -> ContrastToolUtils.formatElapsedTime(anyLong()))
                    .thenReturn("3秒");

            // 执行测试
            PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());

            // 验证DTO中的新增字段
            ResultMonitorDto dto = result.getList().get(0);
            assertEquals("3秒", dto.getElapsedTimeStr());
            assertEquals("周期触发", dto.getTriggerFrom());

            // 验证ContrastToolUtils.formatElapsedTime被调用，且参数大于0（动态计算的耗时）
            mockedContrastToolUtils.verify(() -> ContrastToolUtils.formatElapsedTime(longThat(time -> time > 0)));
        }
    }

    @Test
    @DisplayName("测试查询比对结果列表 - createTime为null时耗时为0")
    void testSelectResultMonitorList_CreateTimeNull() {
        // 准备测试数据 - createTime为null
        ResultMonitorBean beanWithNullCreateTime = new ResultMonitorBean();
        beanWithNullCreateTime.setId(1L);
        beanWithNullCreateTime.setRunRuleId(100L);
        beanWithNullCreateTime.setCreateTime(null); // createTime为null
        beanWithNullCreateTime.setElapsedTime(null); // 耗时为null
        beanWithNullCreateTime.setFrom(StartFromEnums.RETRY.getCode());

        Page<ResultMonitorBean> page = new Page<>(1, 10);
        page.add(beanWithNullCreateTime);
        page.setTotal(1);
        doReturn(page).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), anyInt(), anyInt(), anyInt());

        // Mock ContrastToolUtils.formatElapsedTime静态方法
        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            mockedContrastToolUtils.when(() -> ContrastToolUtils.formatElapsedTime(0L))
                    .thenReturn("0秒");

            // 执行测试
            PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());

            // 验证DTO中的新增字段
            ResultMonitorDto dto = result.getList().get(0);
            assertEquals("0秒", dto.getElapsedTimeStr());
            assertEquals("重试", dto.getTriggerFrom());

            // 验证ContrastToolUtils.formatElapsedTime被调用，参数为0L
            mockedContrastToolUtils.verify(() -> ContrastToolUtils.formatElapsedTime(0L));
        }
    }

    @Test
    @DisplayName("测试查询比对结果列表 - from为null时触发方式为未知")
    void testSelectResultMonitorList_FromNullTriggerUnknown() {
        // 准备测试数据 - from为null
        ResultMonitorBean beanWithNullFrom = new ResultMonitorBean();
        beanWithNullFrom.setId(1L);
        beanWithNullFrom.setRunRuleId(100L);
        beanWithNullFrom.setCreateTime(new Timestamp(System.currentTimeMillis() - 1000));
        beanWithNullFrom.setElapsedTime(1000L);
        beanWithNullFrom.setFrom(null); // from为null

        Page<ResultMonitorBean> page = new Page<>(1, 10);
        page.add(beanWithNullFrom);
        page.setTotal(1);
        doReturn(page).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), anyInt(), anyInt(), isNull());

        // Mock ContrastToolUtils.formatElapsedTime静态方法
        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            mockedContrastToolUtils.when(() -> ContrastToolUtils.formatElapsedTime(1000L))
                    .thenReturn("1秒");

            // 执行测试
            queryDto.setFrom(null);
            PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());

            // 验证DTO中的新增字段
            ResultMonitorDto dto = result.getList().get(0);
            assertEquals("1秒", dto.getElapsedTimeStr());
            assertEquals("未知启动来源", dto.getTriggerFrom()); // from为null时应该返回"未知启动来源"
        }
    }
}