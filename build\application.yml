server:
    port: 8210

contrast:
    mq:
        engine:
            send-task:
                topic: contrastStartFlow
                group: contrastStartFlowGroup
                enabled: true

spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: *************************************************************************************************************************************************************************************
            username: root
            password: ENC(4c4d6cdcc26fd9371a9bcf9f56d15019)
            # 初始化时建立物理连接的个数
            initial-size: 5
            # 连接池的最小空闲数量
            min-idle: 5
            # 连接池最大连接数量
            maxActive: 50
            # 获取连接时最大等待时间，单位毫秒
            maxWait: 60000
            # 既作为检测的间隔时间又作为testWhileIdel执行的依据
            timeBetweenEvictionRunsMillis: 60000
            # 销毁线程时检测当前连接的最后活动时间和当前时间差大于该值时，关闭当前连接(配置连接在池中的最小生存时间)
            minEvictableIdleTimeMillis: 300000
            # 用来检测数据库连接是否有效的sql 必须是一个查询语句
            validationQuery: SELECT 'x'
            # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
            testWhileIdle: true
            # 申请连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
            testOnBorrow: false
            # 归还连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
            testOnReturn: false
            # 是否缓存preparedStatement, 也就是PSCache,PSCache对支持游标的数据库性能提升巨大，比如说oracle,在mysql下建议关闭。
            poolPreparedStatements: true
            # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
            maxPoolPreparedStatementPerConnectionSize: 20
    liquibase:
        contexts: all
        enabled: true
        drop-first: false
    jackson:
        time-zone: GMT+8
    redis:
        cluster:
            #redis集群地址
            nodes: ************:27001,************:27001,************:27001
            max-redirects: 5
        database: 0
        password: ideal123
        timeout: 180s

    servlet:
        multipart:
        max-file-size: 10MB
        max-request-size: 100MB
    cloud:
        config:
            override-none: true
        stream:
            rocketmq:
                binder:
                    name-server: *************:9876
            function:
                definition: contrastReceiveTaskSendResult
            bindings:
                contrastStartFlow-out-0:
                    destination: ${contrast.mq.engine.send-task.topic}
                    content-type: application/json
                    group: ${contrast.mq.engine.send-task.group}
                    binder: rocketmq
                    producer:
                        # 同步发送确保消息不丢失
                        sync: true
                contrastReceiveTaskSendResult-out-0:
                    destination: contrastReceiveTaskSendResult
                    content-type: application/json
                    group: contrastReceiveTaskSendResultGroup
                    binder: rocketmq
mybatis:
    mapper-locations:
        - classpath:mapper/*Mapper.xml
pagehelper:
    reasonable: true
    supportMethodsArguments: true
    params: count=countSql

management:
    endpoints:
        web:
            exposure:
                include: "*"
    metrics:
        export:
            prometheus:
                enabled: true
ideal:
    communication:
        secret:
            salt: "ideal"
            user-info-key: "user"
            secret-token-key: "secretToken"
    common:
        file-path-validate:
            enable: true
        local-validator-message:
            enable: false
            model: ALL_WITH_CLASS_PATH
common:
    security:
        authentication: true
        button:
            # 默认为true：开启按钮权限
            authentication: true

xxl:
    job:
        admin:
            # 调度中心服务部署的地址
            addresses: http://*************:8899/xxl-job-admin
        executor:
            # 执行器AppName
            appname: contrast-executor
            #执行器端口号：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
            port: 8211
            # 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
            logpath: ${user.dir}/logs
            # 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
            logretentiondays: 30
        # 执行器通讯TOKEN,要和调度中心服务部署配置的accessToken一致，要不然无法连接注册
        accessToken: default_token


messagine-rocketmq:
    namesrvAddr: *************:9876
    consumerGroup: scheduleJobReceiveMonitorResultGroup
    topic: scheduleJobReceiveMonitorResult


