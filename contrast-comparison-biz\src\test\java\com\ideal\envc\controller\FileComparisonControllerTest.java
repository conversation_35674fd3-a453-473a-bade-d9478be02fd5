package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.FileComparisonResultDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IFileComparisonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * FileComparisonController的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("FileComparisonController单元测试")
class FileComparisonControllerTest {

    @Mock
    private IFileComparisonService fileComparisonService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @Mock
    private HttpServletResponse response;

    @InjectMocks
    private FileComparisonController fileComparisonController;

    private FileComparisonRequestDto requestDto;
    private FileComparisonResultDto resultDto;
    private UserDto userDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        requestDto = new FileComparisonRequestDto();
        requestDto.setBaselineServer("基线服务器");
        requestDto.setTargetServer("目标服务器");
        requestDto.setSourceContent("源文件内容");
        requestDto.setTargetContent("目标文件内容");
        requestDto.setDescription("测试比较");

        resultDto = new FileComparisonResultDto();
        resultDto.setTotalSourceFiles(10);
        resultDto.setTotalTargetFiles(10);
        resultDto.setConsistentCount(8);
        resultDto.setInconsistentCount(1);
        resultDto.setMissingCount(1);
        resultDto.setExtraCount(0);

        userDto = new UserDto();
        userDto.setFullName("测试用户");
        userDto.setId(1L);
    }

    @Test
    @DisplayName("测试compare方法 - 成功场景")
    void testCompare_Success() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class))).thenReturn(resultDto);

        // 执行测试方法
        R<FileComparisonResultDto> result = fileComparisonController.compare(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(resultDto, result.getData());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).compareFileContents(requestDto);
    }

    @Test
    @DisplayName("测试compare方法 - 业务异常")
    void testCompare_BusinessException() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("比较失败"));

        // 执行测试方法
        R<FileComparisonResultDto> result = fileComparisonController.compare(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertTrue(result.getMessage().contains("比较失败"));
        assertNull(result.getData());

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).compareFileContents(requestDto);
    }

    @Test
    @DisplayName("测试compare方法 - 系统异常")
    void testCompare_SystemException() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenThrow(new RuntimeException("系统异常"));

        // 执行测试方法
        R<FileComparisonResultDto> result = fileComparisonController.compare(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertTrue(result.getMessage().contains("系统异常"));
        assertNull(result.getData());

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).compareFileContents(requestDto);
    }

    @Test
    @DisplayName("测试export方法 - 成功场景")
    void testExport_Success() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doNothing().when(fileComparisonService).exportComparisonResult(any(FileComparisonRequestDto.class), any(HttpServletResponse.class));

        // 执行测试方法
        assertDoesNotThrow(() -> fileComparisonController.export(requestDto, response));

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).exportComparisonResult(requestDto, response);
    }

    @Test
    @DisplayName("测试export方法 - 业务异常")
    void testExport_BusinessException() throws ContrastBusinessException, IOException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doThrow(new ContrastBusinessException("导出失败")).when(fileComparisonService)
                .exportComparisonResult(any(FileComparisonRequestDto.class), any(HttpServletResponse.class));

        // Mock response
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        when(response.getWriter()).thenReturn(printWriter);

        // 执行测试方法
        assertDoesNotThrow(() -> fileComparisonController.export(requestDto, response));

        // 验证response设置
        verify(response).reset();
        verify(response).setContentType("application/json;charset=utf-8");
        verify(response).getWriter();

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).exportComparisonResult(requestDto, response);
    }

    @Test
    @DisplayName("测试export方法 - 系统异常")
    void testExport_SystemException() throws ContrastBusinessException, IOException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doThrow(new RuntimeException("系统异常")).when(fileComparisonService)
                .exportComparisonResult(any(FileComparisonRequestDto.class), any(HttpServletResponse.class));

        // Mock response
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        when(response.getWriter()).thenReturn(printWriter);

        // 执行测试方法
        assertDoesNotThrow(() -> fileComparisonController.export(requestDto, response));

        // 验证response设置
        verify(response).reset();
        verify(response).setContentType("application/json;charset=utf-8");
        verify(response).getWriter();

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).exportComparisonResult(requestDto, response);
    }

    @Test
    @DisplayName("测试export方法 - 写入响应异常")
    void testExport_WriteResponseException() throws ContrastBusinessException, IOException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doThrow(new ContrastBusinessException("导出失败")).when(fileComparisonService)
                .exportComparisonResult(any(FileComparisonRequestDto.class), any(HttpServletResponse.class));

        // Mock response抛出IOException
        when(response.getWriter()).thenThrow(new IOException("写入失败"));

        // 执行测试方法
        assertDoesNotThrow(() -> fileComparisonController.export(requestDto, response));

        // 验证response设置
        verify(response).reset();
        verify(response).setContentType("application/json;charset=utf-8");
        verify(response).getWriter();

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).exportComparisonResult(requestDto, response);
    }

    @Test
    @DisplayName("测试quickCompare方法 - 成功场景")
    void testQuickCompare_Success() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class))).thenReturn(resultDto);

        // 执行测试方法
        R<String> result = fileComparisonController.quickCompare(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().contains("比较完成"));
        assertTrue(result.getData().contains("基线文件：10个"));
        assertTrue(result.getData().contains("目标文件：10个"));
        assertTrue(result.getData().contains("一致：8个"));

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).compareFileContents(requestDto);
    }

    @Test
    @DisplayName("测试quickCompare方法 - 有不一致文件")
    void testQuickCompare_WithInconsistentFiles() throws ContrastBusinessException {
        // 修改结果数据
        resultDto.setInconsistentCount(2);
        resultDto.setMissingCount(1);
        resultDto.setExtraCount(1);

        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class))).thenReturn(resultDto);

        // 执行测试方法
        R<String> result = fileComparisonController.quickCompare(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().contains("不一致：2个"));
        assertTrue(result.getData().contains("缺失：1个"));
        assertTrue(result.getData().contains("多出：1个"));

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).compareFileContents(requestDto);
    }

    @Test
    @DisplayName("测试quickCompare方法 - 业务异常")
    void testQuickCompare_BusinessException() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenThrow(new ContrastBusinessException("快速比较失败"));

        // 执行测试方法
        R<String> result = fileComparisonController.quickCompare(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertTrue(result.getMessage().contains("快速比较失败"));
        assertNull(result.getData());

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).compareFileContents(requestDto);
    }

    @Test
    @DisplayName("测试quickCompare方法 - 系统异常")
    void testQuickCompare_SystemException() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(fileComparisonService.compareFileContents(any(FileComparisonRequestDto.class)))
                .thenThrow(new RuntimeException("系统异常"));

        // 执行测试方法
        R<String> result = fileComparisonController.quickCompare(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertTrue(result.getMessage().contains("系统异常"));
        assertNull(result.getData());

        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(fileComparisonService).compareFileContents(requestDto);
    }

    @Test
    @DisplayName("测试构造函数")
    void testConstructor() {
        // 创建新的控制器实例
        FileComparisonController controller = new FileComparisonController(fileComparisonService, userinfoComponent);
        
        // 验证实例创建成功
        assertNotNull(controller);
    }
}
