# HTML比对功能响应码使用指南

## 概述

HTML比对功能严格遵循项目统一的ResponseCodeEnum枚举类规范，确保所有API接口返回统一格式的响应码和描述信息。

## 响应码分类

### 1. 成功响应码
| 响应码 | 描述 | 使用场景 |
|--------|------|----------|
| 10000  | 成功 | 所有成功的操作 |

### 2. 比对解析相关异常码 (1306xx)
| 响应码 | 描述 | 使用场景 |
|--------|------|----------|
| 130600 | 比对解析失败 | 通用解析失败异常 |
| 130601 | HTML内容为空 | HTML内容字符串为空或null |
| 130602 | 流程ID不能为空 | flowId参数为空或null |
| 130603 | 流程结果不存在 | 根据flowId未查询到流程结果数据 |
| 130604 | 流程详情不存在 | 根据flowId未查询到流程详情数据 |
| 130605 | 流程内容为空 | 流程结果的content和stderr都为空 |
| 130606 | 内容解析失败 | ContentCustomDto解析失败 |
| 130607 | HTML解析失败 | HTML格式解析失败 |
| 130608 | JSON解析失败 | JSON格式解析失败 |
| 130609 | 不支持的数据格式 | 数据格式不被支持 |

### 3. 导出相关异常码 (1307xx)
| 响应码 | 描述 | 使用场景 |
|--------|------|----------|
| 130700 | 导出失败 | 通用导出失败异常 |
| 130701 | Excel导出失败 | Excel文件生成失败 |
| 130702 | 文件写入失败 | 文件写入磁盘失败 |
| 130703 | 模板文件不存在 | Excel模板文件不存在 |
| 130704 | 导出数据为空 | 没有数据可供导出 |
| 130705 | 导出权限不足 | 用户没有导出权限 |

### 4. 系统级异常码
| 响应码 | 描述 | 使用场景 |
|--------|------|----------|
| 139999 | 系统异常 | 未预期的系统异常 |

## API接口响应格式

### 成功响应格式
```json
{
    "code": "10000",
    "message": "成功",
    "data": {
        // 具体的响应数据
    }
}
```

### 失败响应格式
```json
{
    "code": "130602",
    "message": "流程ID不能为空",
    "data": null
}
```

## Controller层实现规范

### 1. 统一导入
```java
import com.ideal.envc.model.enums.ResponseCodeEnum;
```

### 2. 成功响应
```java
return R.ok(ResponseCodeEnum.SUCCESS.getCode(), result, ResponseCodeEnum.SUCCESS.getDesc());
```

### 3. 业务异常响应
```java
// 根据异常消息判断具体响应码
private R<String> getBusinessExceptionResponse(ContrastBusinessException e) {
    String message = e.getMessage();
    
    if (message.contains("流程ID不能为空")) {
        return R.fail(ResponseCodeEnum.FLOW_ID_EMPTY.getCode(), message);
    } else if (message.contains("流程结果不存在")) {
        return R.fail(ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode(), message);
    }
    // ... 其他异常映射
    else {
        return R.fail(ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode(), message);
    }
}
```

### 4. 系统异常响应
```java
catch (Exception e) {
    logger.error("系统异常", e);
    return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
}
```

## 异常消息与响应码映射规则

### 比对解析异常映射
| 异常消息关键词 | 响应码 |
|---------------|--------|
| "流程ID不能为空" | 130602 |
| "流程结果不存在", "未查询到对应的流程结果数据" | 130603 |
| "流程详情不存在", "未查询到对应设备相关信息" | 130604 |
| "流程内容为空", "流程结果内容为空" | 130605 |
| "HTML内容为空" | 130601 |
| "解析失败", "内容格式不正确" | 130606 |
| "HTML解析失败" | 130607 |
| "JSON解析失败" | 130608 |
| 其他解析相关异常 | 130600 |

### 导出异常映射
| 异常消息关键词 | 响应码 |
|---------------|--------|
| "导出数据为空" | 130704 |
| "Excel导出失败" | 130701 |
| "文件写入失败" | 130702 |
| "权限不足" | 130705 |
| "系统异常" | 139999 |
| 其他导出相关异常 | 130700 |

## 使用示例

### 1. 解析HTML比对内容
```http
POST /htmlComparison/parse
{
    "flowId": 12345
}

# 成功响应
{
    "code": "10000",
    "message": "成功",
    "data": {
        "totalSourceFiles": 50,
        "totalTargetFiles": 48,
        "consistentCount": 45,
        "inconsistentCount": 3,
        "missingCount": 2,
        "extraCount": 0
    }
}

# 失败响应
{
    "code": "130602",
    "message": "流程ID不能为空",
    "data": null
}
```

### 2. 导出Excel
```http
POST /htmlComparison/export
{
    "flowId": 12345
}

# 成功：返回Excel文件流
# 失败：返回JSON错误信息
{
    "code": "130603",
    "message": "流程结果不存在"
}
```

## 测试验证

### 单元测试示例
```java
@Test
@DisplayName("测试流程ID为空异常")
void testFlowIdEmpty() throws Exception {
    when(htmlComparisonService.parseHtmlComparison(any()))
            .thenThrow(new ContrastBusinessException("流程ID不能为空"));

    R<HtmlComparisonResultDto> response = controller.parseHtmlComparison(request);

    assertEquals(ResponseCodeEnum.FLOW_ID_EMPTY.getCode(), response.getCode());
    assertEquals("流程ID不能为空", response.getMessage());
}
```

## 最佳实践

### 1. 异常处理原则
- **业务异常**：使用具体的业务响应码
- **系统异常**：统一使用系统异常响应码
- **参数验证**：使用对应的参数验证响应码

### 2. 日志记录规范
```java
// 业务异常 - 使用warn级别
logger.warn("HTML比对解析业务异常：{}", e.getMessage());

// 系统异常 - 使用error级别
logger.error("HTML比对解析系统异常", e);
```

### 3. 响应消息规范
- **成功消息**：使用枚举定义的标准描述
- **失败消息**：使用异常的具体消息内容
- **系统异常**：使用枚举定义的通用描述

### 4. 扩展新响应码
1. 在`ResponseCodeEnum`中添加新的枚举值
2. 更新响应码文档
3. 在Controller中添加对应的异常映射
4. 编写单元测试验证

## 注意事项

1. **响应码唯一性**：确保每个响应码在系统中唯一
2. **消息一致性**：异常消息与响应码描述保持一致
3. **向后兼容**：新增响应码不影响现有功能
4. **文档同步**：及时更新响应码文档
5. **测试覆盖**：确保所有响应码都有对应的测试用例

通过严格遵循这些规范，确保HTML比对功能的响应码使用统一、规范、易维护。
