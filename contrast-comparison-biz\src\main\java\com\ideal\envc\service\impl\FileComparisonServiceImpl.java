package com.ideal.envc.service.impl;


import com.ideal.envc.model.enums.FileComparisonStrategy;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.FileComparisonExportDto;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.FileComparisonResultDto;
import com.ideal.envc.model.dto.FileInfoDto;
import com.ideal.envc.service.IFileComparisonService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件比较服务实现类
 *
 * <AUTHOR>
 */
@Service
public class FileComparisonServiceImpl implements IFileComparisonService {
    private static final Logger logger = LoggerFactory.getLogger(FileComparisonServiceImpl.class);

    /**
     * 文件信息解析正则表达式
     * 匹配格式：文件路径 (size: 大小, permissions: 权限, MD5: MD5值)
     */
    private static final Pattern FILE_PATTERN = Pattern.compile(
            "^(.+?)\\s+\\(size:\\s*(.+?),\\s*permissions:\\s*(.+?),\\s*MD5:\\s*(.+?)\\)$"
    );

    /**
     * 状态常量
     */
    private static final String STATUS_CONSISTENT = "一致";
    private static final String STATUS_INCONSISTENT = "不一致";
    private static final String STATUS_MISSING = "缺失";
    private static final String STATUS_EXTRA = "多出";

    @Override
    public FileComparisonResultDto compareFileContents(FileComparisonRequestDto request) throws ContrastBusinessException {
        logger.info("开始比较文件内容，基线服务器：{}，目标服务器：{}",
                request.getBaselineServer(), request.getTargetServer());

        // 验证输入参数 - 只有当源内容和目标内容都为空时才抛出异常
        if (StringUtils.isBlank(request.getSourceContent()) && StringUtils.isBlank(request.getTargetContent())) {
            throw new ContrastBusinessException("源内容和目标内容不能同时为空");
        }

        // 处理null策略，设置默认值
        if (request.getComparisonStrategy() == null) {
            request.setComparisonStrategy(FileComparisonStrategy.COMPREHENSIVE);
        }

        try {
            // 解析源文件和目标文件
            List<FileInfoDto> sourceFiles = parseFileContent(request.getSourceContent());
            List<FileInfoDto> targetFiles = parseFileContent(request.getTargetContent());

            logger.info("解析完成，源文件数量：{}，目标文件数量：{}", sourceFiles.size(), targetFiles.size());

            // 创建目标文件映射，以文件路径为key
            Map<String, FileInfoDto> targetFileMap = new HashMap<>();
            for (FileInfoDto file : targetFiles) {
                targetFileMap.put(file.getFilePath(), file);
            }

            // 比较结果列表
            List<FileInfoDto> consistentFiles = new ArrayList<>();
            List<FileInfoDto> inconsistentFiles = new ArrayList<>();
            List<FileInfoDto> missingFiles = new ArrayList<>();

            // 遍历源文件进行比较
            for (FileInfoDto sourceFile : sourceFiles) {
                FileInfoDto targetFile = targetFileMap.get(sourceFile.getFilePath());

                if (targetFile == null) {
                    // 目标文件不存在，标记为缺失
                    sourceFile.setStatus(STATUS_MISSING);
                    sourceFile.setRemark("目标服务器中不存在此文件");
                    missingFiles.add(sourceFile);
                } else if (isFilesConsistent(sourceFile, targetFile, request.getComparisonStrategy())) {
                    // 文件一致，标记为一致
                    sourceFile.setStatus(STATUS_CONSISTENT);
                    sourceFile.setRemark("文件一致");
                    consistentFiles.add(sourceFile);
                } else {
                    // 文件不一致，标记为不一致
                    sourceFile.setStatus(STATUS_INCONSISTENT);
                    sourceFile.setRemark(buildInconsistentRemark(sourceFile, targetFile, request.getComparisonStrategy()));
                    inconsistentFiles.add(sourceFile);
                }

                // 从目标文件映射中移除已处理的文件
                targetFileMap.remove(sourceFile.getFilePath());
            }

            // 剩余的目标文件为多出的文件
            List<FileInfoDto> extraFiles = new ArrayList<>();
            for (FileInfoDto extraFile : targetFileMap.values()) {
                extraFile.setStatus(STATUS_EXTRA);
                extraFile.setRemark("基线服务器中不存在此文件");
                extraFiles.add(extraFile);
            }

            // 构建比较结果
            FileComparisonResultDto result = buildComparisonResult(
                    request, sourceFiles.size(), targetFiles.size(),
                    consistentFiles, inconsistentFiles, missingFiles, extraFiles
            );

            logger.info("文件比较完成，结果：{}", result);
            return result;

        } catch (Exception e) {
            logger.error("文件比较过程中发生异常", e);
            throw new ContrastBusinessException("文件比较失败：" + e.getMessage());
        }
    }

    @Override
    public void exportComparisonResult(FileComparisonRequestDto request, HttpServletResponse response) throws ContrastBusinessException {
        FileComparisonResultDto result = compareFileContents(request);
        exportComparisonResult(request,result, response);
    }


    @Override
    public void exportComparisonResult(FileComparisonRequestDto request,FileComparisonResultDto result, HttpServletResponse response) throws ContrastBusinessException {

        try {
            // 设置响应头
            String fileName = "目录比较结果_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));

            // 准备导出数据
            List<FileComparisonExportDto> exportData = prepareExportData(result,  request);

            // 使用FastExcel导出，严格按照图片格式
            exportWithFastExcel(response.getOutputStream(), exportData, result, request);

            logger.info("文件比较结果导出完成，共导出{}条记录", exportData.size());

        } catch (IOException e) {
            logger.error("导出文件比较结果时发生IO异常", e);
            throw new ContrastBusinessException("导出Excel文件失败：" + e.getMessage());
        } catch (Exception e) {
            logger.error("导出文件比较结果时发生异常", e);
            throw new ContrastBusinessException("导出Excel文件失败：" + e.getMessage());
        }
    }

    /**
     * 解析文件内容字符串
     *
     * @param content 文件内容字符串
     * @return 文件信息列表
     */
    private List<FileInfoDto> parseFileContent(String content) {
        List<FileInfoDto> files = new ArrayList<>();
        
        if (StringUtils.isBlank(content)) {
            return files;
        }

        String[] lines = content.split("\n");
        
        for (String line : lines) {
            if (StringUtils.isNotBlank(line)) {
                Matcher matcher = FILE_PATTERN.matcher(line.trim());
                
                if (matcher.matches()) {
                    FileInfoDto file = new FileInfoDto(
                            matcher.group(1).trim(),  // 文件路径
                            matcher.group(2).trim(),  // 文件大小
                            matcher.group(3).trim(),  // 权限
                            matcher.group(4).trim()   // MD5
                    );
                    files.add(file);
                } else {
                    logger.warn("无法解析的文件信息行：{}", line);
                }
            }
        }
        
        return files;
    }

    /**
     * 构建比较结果
     */
    private FileComparisonResultDto buildComparisonResult(
            FileComparisonRequestDto request, int totalSourceFiles, int totalTargetFiles,
            List<FileInfoDto> consistentFiles, List<FileInfoDto> inconsistentFiles,
            List<FileInfoDto> missingFiles, List<FileInfoDto> extraFiles) {

        FileComparisonResultDto result = new FileComparisonResultDto();
        result.setBaselineServer(request.getBaselineServer());
        result.setTargetServer(request.getTargetServer());
        result.setDescription(request.getDescription());
        result.setTotalSourceFiles(totalSourceFiles);
        result.setTotalTargetFiles(totalTargetFiles);
        result.setConsistentCount(consistentFiles.size());
        result.setInconsistentCount(inconsistentFiles.size());
        result.setMissingCount(missingFiles.size());
        result.setExtraCount(extraFiles.size());

        // 计算比率
        if (totalSourceFiles > 0) {
            result.setConsistentRate(calculateRate(consistentFiles.size(), totalSourceFiles));
            result.setInconsistentRate(calculateRate(inconsistentFiles.size(), totalSourceFiles));
            result.setMissingRate(calculateRate(missingFiles.size(), totalSourceFiles));
        }
        
        if (totalTargetFiles > 0) {
            result.setExtraRate(calculateRate(extraFiles.size(), totalTargetFiles));
        }

        result.setConsistentFiles(consistentFiles);
        result.setInconsistentFiles(inconsistentFiles);
        result.setMissingFiles(missingFiles);
        result.setExtraFiles(extraFiles);

        return result;
    }

    /**
     * 计算比率
     */
    private BigDecimal calculateRate(int count, int total) {
        if (total == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(count)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);
    }


    /**
     * 准备导出数据 - 支持IP和主机名参数，严格按照图片格式
     */
    private List<FileComparisonExportDto> prepareExportData(FileComparisonResultDto result,FileComparisonRequestDto request) {
        List<FileComparisonExportDto> exportData = new ArrayList<>();

        // 基线服务器汇总行（缺失、多出、不一致、一致列为空）
        FileComparisonExportDto baselineRow = new FileComparisonExportDto();
        baselineRow.setServerType("基线服务器");
        baselineRow.setIp(request.getBaseServerIp() != null ? request.getBaseServerIp() : "");
        baselineRow.setHostname(request.getBaselineServer() != null ? request.getBaselineServer() : "");
        baselineRow.setTotal(result.getTotalSourceFiles());
        // 基线服务器的统计列为空，因为统计是相对于基线服务器而言的
        baselineRow.setMissing(null);
        baselineRow.setExtra(null);
        baselineRow.setInconsistent(null);
        baselineRow.setConsistent(null);
        exportData.add(baselineRow);

        // 目标服务器汇总行（包含所有统计数据）
        FileComparisonExportDto targetRow = new FileComparisonExportDto();
        targetRow.setServerType("目标服务器");
        targetRow.setIp(request.getTargetServerIp() != null ? request.getTargetServerIp() : "");
        targetRow.setHostname(request.getTargetServer() != null ? request.getTargetServer() : "");
        targetRow.setTotal(result.getTotalTargetFiles());
        // 目标服务器显示相对于基线服务器的统计数据
        targetRow.setMissing(result.getMissingCount());
        targetRow.setExtra(result.getExtraCount());
        targetRow.setInconsistent(result.getInconsistentCount());
        targetRow.setConsistent(result.getConsistentCount());
        exportData.add(targetRow);

        return exportData;
    }



    /**
     * 使用POI导出Excel - 包含两个Sheet：比对结果和一致文件列表
     */
    private void exportWithFastExcel(OutputStream outputStream, List<FileComparisonExportDto> exportData, FileComparisonResultDto result,FileComparisonRequestDto request) throws IOException {
        logger.info("使用POI导出比对报表，数据行数：{}", exportData.size());

        try {
            // 使用原生POI来实现复杂的格式要求
            exportWithPOI(outputStream, exportData, result,request);

        } catch (Exception e) {
            logger.error("POI导出失败", e);
            throw new IOException("Excel导出失败：" + e.getMessage(), e);
        }
    }



    /**
     * 使用POI导出Excel - 包含两个Sheet：比对结果和一致文件列表
     */
    private void exportWithPOI(OutputStream outputStream, List<FileComparisonExportDto> exportData, FileComparisonResultDto result,FileComparisonRequestDto request) throws IOException {
        Workbook workbook = new XSSFWorkbook();

        // 创建样式
        CellStyle titleStyle = createTitleStyle(workbook);
        CellStyle descriptionStyle = createDescriptionStyle(workbook);
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);
        CellStyle filePathStyle = createFilePathStyle(workbook);

        // 创建不同类型文件的样式（带背景颜色）
        CellStyle missingFileStyle = createMissingFileStyle(workbook);
        CellStyle extraFileStyle = createExtraFileStyle(workbook);
        CellStyle inconsistentFileStyle = createInconsistentFileStyle(workbook);
        CellStyle consistentFileStyle = createConsistentFileStyle(workbook);

        // 第一个Sheet：比对结果
        Sheet resultSheet = workbook.createSheet("比对结果");
        createResultSheet(resultSheet, exportData, result,request, titleStyle, descriptionStyle, headerStyle, dataStyle, filePathStyle,
                         missingFileStyle, extraFileStyle, inconsistentFileStyle);

        // 第二个Sheet：一致文件列表
        Sheet consistentSheet = workbook.createSheet("一致文件列表");
        createConsistentFilesSheet(consistentSheet, result, headerStyle, consistentFileStyle);

        // 写入输出流
        workbook.write(outputStream);
        workbook.close();

        logger.info("POI Excel导出完成，包含比对结果和一致文件列表两个Sheet");
    }

    /**
     * 创建标题样式（黑色粗体）
     */
    private CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);
        font.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(font);

        return style;
    }

    /**
     * 创建说明文字样式（红色粗体）
     */
    private CellStyle createDescriptionStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);
        font.setColor(IndexedColors.RED.getIndex());
        style.setFont(font);

        return style;
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建文件路径样式（确保完整边框）
     */
    private CellStyle createFilePathStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置完整边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建缺失文件样式（浅红色背景）
     */
    private CellStyle createMissingFileStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置浅红色背景
        style.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建多出文件样式（浅绿色背景）
     */
    private CellStyle createExtraFileStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置浅绿色背景
        style.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建不一致文件样式（浅黄色背景）
     */
    private CellStyle createInconsistentFileStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置浅黄色背景
        style.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建一致文件样式（浅青色背景）
     */
    private CellStyle createConsistentFileStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置浅青色背景 - 青色代表清新、和谐、符合预期的状态
        style.setFillForegroundColor(IndexedColors.AQUA.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建比对结果Sheet
     */
    private void createResultSheet(Sheet sheet, List<FileComparisonExportDto> exportData, FileComparisonResultDto result,FileComparisonRequestDto request,
                                   CellStyle titleStyle, CellStyle descriptionStyle, CellStyle headerStyle,
                                   CellStyle dataStyle, CellStyle filePathStyle,
                                  CellStyle missingFileStyle, CellStyle extraFileStyle, CellStyle inconsistentFileStyle) {
        int currentRow = 0;

        // 1. 添加标题部分（黑色粗体）
        currentRow = addTitleSection(sheet, result,request, currentRow, titleStyle);

        // 2. 添加空行
        currentRow++;

        // 3. 添加说明文字部分（红色粗体）
        currentRow = addDescriptionSection(sheet, result, currentRow, descriptionStyle);

        // 4. 添加空行
        currentRow++;

        // 5. 添加汇总表格部分（居中显示，列头有背景色）
        currentRow = addSummarySection(sheet, exportData, currentRow, headerStyle, dataStyle);

        // 6. 添加空行
        currentRow++;

        // 7. 添加详细文件列表部分（文件路径合并多列，居左显示）
        currentRow = addFileListSection(sheet, result, currentRow, headerStyle, filePathStyle,
                                       missingFileStyle, extraFileStyle, inconsistentFileStyle);

        // 设置列宽
        setColumnWidths(sheet);
    }

    /**
     * 创建一致文件列表Sheet
     */
    private void createConsistentFilesSheet(Sheet sheet, FileComparisonResultDto result, CellStyle headerStyle, CellStyle consistentFileStyle) {
        int currentRow = 0;

        // 创建表头
        Row headerRow = sheet.createRow(currentRow);
        Cell typeCell = headerRow.createCell(0);
        typeCell.setCellValue("类型");
        typeCell.setCellStyle(headerStyle);

        Cell pathCell = headerRow.createCell(1);
        pathCell.setCellValue("文件路径");
        pathCell.setCellStyle(headerStyle);

        Cell diffCell = headerRow.createCell(2);
        diffCell.setCellValue("差异说明");
        diffCell.setCellStyle(headerStyle);

        // 合并文件路径列（B到G列，为差异说明列留出空间）
        sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow, 1, 6));

        // 为合并区域的其他单元格也设置样式和边框
        for (int i = 3; i <= 6; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellStyle(headerStyle);
        }

        // 差异说明列（H列）
        Cell diffHeaderCell = headerRow.createCell(7);
        diffHeaderCell.setCellValue("差异说明");
        diffHeaderCell.setCellStyle(headerStyle);

        currentRow++;

        // 添加一致文件列表
        for (FileInfoDto file : result.getConsistentFiles()) {
            Row row = sheet.createRow(currentRow);

            Cell typeCell1 = row.createCell(0);
            typeCell1.setCellValue("一致");
            typeCell1.setCellStyle(consistentFileStyle);

            Cell pathCell1 = row.createCell(1);
            pathCell1.setCellValue(file.getFilePath());
            pathCell1.setCellStyle(consistentFileStyle);

            // 合并文件路径列（B到G列，为差异说明列留出空间）
            sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow, 1, 6));

            // 为合并区域的其他单元格也设置样式和边框
            for (int i = 2; i <= 6; i++) {
                Cell cell = row.createCell(i);
                cell.setCellStyle(consistentFileStyle);
            }

            // 差异说明列
            Cell diffCell7 = row.createCell(7);
            diffCell7.setCellValue("文件完全一致");
            diffCell7.setCellStyle(consistentFileStyle);

            currentRow++;
        }

        // 设置列宽
        setColumnWidths(sheet);
    }

    /**
     * 添加标题部分（黑色粗体）
     */
    private int addTitleSection(Sheet sheet, FileComparisonResultDto result,FileComparisonRequestDto request, int startRow, CellStyle style) {
        int currentRow = startRow;
        // 标题文字列表
        String[] titles = {
            "环境一致性比对结果报告",
            "比对对象：" + request.getBaselineServer() + " vs " + request.getTargetServer(),
            "所属系统：" + request.getBusinessSystemName(),
            "对应中心：" + request.getSourceCenterName() + " vs " + request.getTargetCenterName(),
            "导出时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        };

        for (String title : titles) {
            Row row = sheet.createRow(currentRow);
            Cell cell = row.createCell(0);
            cell.setCellValue(title);
            cell.setCellStyle(style);

            // 合并A到H列（8列）
            sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow, 0, 7));

            currentRow++;
        }

        return currentRow;
    }

    /**
     * 添加说明文字部分（红色粗体）
     */
    private int addDescriptionSection(Sheet sheet, FileComparisonResultDto result, int startRow, CellStyle style) {
        int currentRow = startRow;

        // 创建说明文字列表（红色粗体）
        String[] descriptions = {
            "比对规则:分别生成基线和抽检服务显应用目录文件的基本信息，然后比对（大小、权限、MD5）",
            "计算公式:基线汇总数=抽检汇总数-抽检多出数 +抽检缺失数",
            "路径说明:",
            "缺失路径:显示的是基线服务器的绝对路径信息，即抽检服务器和基线服务器相比，抽检服务器缺失的文件信息",
            "多出路径:显示的是抽检服务器的绝对路径信息，即抽检服务器和基线服务器相比，抽检服务器缺失的文件信息",
            "不一致路径:显示的是抽检服务器的绝对路经信息"
        };

        for (String description : descriptions) {
            Row row = sheet.createRow(currentRow);
            Cell cell = row.createCell(0);
            cell.setCellValue(description);
            cell.setCellStyle(style);

            // 合并A到H列（8列）
            sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow, 0, 7));

            currentRow++;
        }

        return currentRow;
    }

    /**
     * 添加汇总表格部分
     */
    private int addSummarySection(Sheet sheet, List<FileComparisonExportDto> exportData, int startRow, CellStyle headerStyle, CellStyle dataStyle) {
        int currentRow = startRow;

        // 创建表头
        Row headerRow = sheet.createRow(currentRow);
        String[] headers = {"服务器类型", "hostname", "IP", "汇总", "缺失", "多出", "不一致", "一致"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        currentRow++;

        // 添加数据行
        for (FileComparisonExportDto data : exportData) {
            Row dataRow = sheet.createRow(currentRow);

            // 服务器类型
            Cell cell0 = dataRow.createCell(0);
            cell0.setCellValue(data.getServerType() != null ? data.getServerType() : "");
            cell0.setCellStyle(dataStyle);

            // hostname
            Cell cell1 = dataRow.createCell(1);
            cell1.setCellValue(data.getHostname() != null ? data.getHostname() : "");
            cell1.setCellStyle(dataStyle);

            // IP
            Cell cell2 = dataRow.createCell(2);
            cell2.setCellValue(data.getIp() != null ? data.getIp() : "");
            cell2.setCellStyle(dataStyle);



            // 汇总
            Cell cell3 = dataRow.createCell(3);
            cell3.setCellValue(data.getTotal() != null ? data.getTotal() : 0);
            cell3.setCellStyle(dataStyle);

            // 缺失（基线服务器为空，目标服务器显示数值）
            Cell cell4 = dataRow.createCell(4);
            if (data.getMissing() != null) {
                cell4.setCellValue(data.getMissing());
            } else {
                cell4.setCellValue(""); // 基线服务器缺失列为空
            }
            cell4.setCellStyle(dataStyle);

            // 多出（基线服务器为空，目标服务器显示数值）
            Cell cell5 = dataRow.createCell(5);
            if (data.getExtra() != null) {
                cell5.setCellValue(data.getExtra());
            } else {
                cell5.setCellValue(""); // 基线服务器多出列为空
            }
            cell5.setCellStyle(dataStyle);

            // 不一致（基线服务器为空，目标服务器显示数值）
            Cell cell6 = dataRow.createCell(6);
            if (data.getInconsistent() != null) {
                cell6.setCellValue(data.getInconsistent());
            } else {
                cell6.setCellValue(""); // 基线服务器不一致列为空
            }
            cell6.setCellStyle(dataStyle);

            // 一致（基线服务器为空，目标服务器显示数值）
            Cell cell7 = dataRow.createCell(7);
            if (data.getConsistent() != null) {
                cell7.setCellValue(data.getConsistent());
            } else {
                cell7.setCellValue(""); // 基线服务器一致列为空
            }
            cell7.setCellStyle(dataStyle);

            currentRow++;
        }

        return currentRow;
    }

    /**
     * 添加详细文件列表部分（确保所有合并单元格都有边框）
     */
    private int addFileListSection(Sheet sheet, FileComparisonResultDto result, int startRow, CellStyle headerStyle, CellStyle filePathStyle,
                                  CellStyle missingFileStyle, CellStyle extraFileStyle, CellStyle inconsistentFileStyle) {
        int currentRow = startRow;

        // 创建文件列表表头
        currentRow = createFileListHeader(sheet, currentRow, headerStyle);

        // 添加各类型文件
        currentRow = addMissingFiles(sheet, result.getMissingFiles(), currentRow, missingFileStyle);
        currentRow = addExtraFiles(sheet, result.getExtraFiles(), currentRow, extraFileStyle);
        currentRow = addInconsistentFiles(sheet, result.getInconsistentFiles(), currentRow, inconsistentFileStyle);

        return currentRow;
    }

    /**
     * 创建文件列表表头
     */
    private int createFileListHeader(Sheet sheet, int currentRow, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(currentRow);

        Cell typeCell = headerRow.createCell(0);
        typeCell.setCellValue("类型");
        typeCell.setCellStyle(headerStyle);

        Cell pathCell = headerRow.createCell(1);
        pathCell.setCellValue("文件路径");
        pathCell.setCellStyle(headerStyle);

        Cell diffCell = headerRow.createCell(7);
        diffCell.setCellValue("差异说明");
        diffCell.setCellStyle(headerStyle);

        // 合并文件路径列（B到G列，为差异说明列留出空间）
        sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow, 1, 6));
        for (int i = 2; i <= 6; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellStyle(headerStyle);
        }

        return currentRow + 1;
    }

    /**
     * 添加缺失文件行
     */
    private int addMissingFiles(Sheet sheet, List<FileInfoDto> missingFiles, int currentRow, CellStyle missingFileStyle) {
        for (FileInfoDto file : missingFiles) {
            currentRow = createFileRow(sheet, currentRow, "缺失", file.getFilePath(), "文件被删除", missingFileStyle);
        }
        return currentRow;
    }

    /**
     * 添加多出文件行
     */
    private int addExtraFiles(Sheet sheet, List<FileInfoDto> extraFiles, int currentRow, CellStyle extraFileStyle) {
        for (FileInfoDto file : extraFiles) {
            currentRow = createFileRow(sheet, currentRow, "多出", file.getFilePath(), "新增文件", extraFileStyle);
        }
        return currentRow;
    }

    /**
     * 添加不一致文件行
     */
    private int addInconsistentFiles(Sheet sheet, List<FileInfoDto> inconsistentFiles, int currentRow, CellStyle inconsistentFileStyle) {
        for (FileInfoDto file : inconsistentFiles) {
            String diffDescription = buildDifferenceDescription(file.getRemark());
            currentRow = createFileRow(sheet, currentRow, "不一致", file.getFilePath(), diffDescription, inconsistentFileStyle);
        }
        return currentRow;
    }

    /**
     * 创建文件行（通用方法）
     */
    private int createFileRow(Sheet sheet, int currentRow, String type, String filePath, String diffDescription, CellStyle cellStyle) {
        Row row = sheet.createRow(currentRow);

        // 类型列
        Cell typeCell = row.createCell(0);
        typeCell.setCellValue(type);
        typeCell.setCellStyle(cellStyle);

        // 文件路径列
        Cell pathCell = row.createCell(1);
        pathCell.setCellValue(filePath);
        pathCell.setCellStyle(cellStyle);

        // 合并文件路径列（B到G列，为差异说明列留出空间）
        sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow, 1, 6));
        for (int i = 2; i <= 6; i++) {
            Cell cell = row.createCell(i);
            cell.setCellStyle(cellStyle);
        }

        // 差异说明列
        Cell diffCell = row.createCell(7);
        diffCell.setCellValue(diffDescription);
        diffCell.setCellStyle(cellStyle);

        return currentRow + 1;
    }

    /**
     * 构建差异说明
     *
     * @param remark 文件的备注信息
     * @return 格式化的差异说明
     */
    private String buildDifferenceDescription(String remark) {
        if (StringUtils.isBlank(remark)) {
            return "文件内容不一致";
        }

        // 如果remark包含"文件不一致："，则提取后面的具体差异信息
        if (remark.contains("文件不一致：")) {
            String differences = remark.substring(remark.indexOf("文件不一致：") + 6);
            return differences;
        }

        // 如果remark包含"MD5值不同"，则返回MD5差异说明
        if (remark.contains("MD5值不同")) {
            return "文件内容变化（MD5不同）";
        }

        // 其他情况返回原始remark或默认说明
        return StringUtils.isNotBlank(remark) ? remark : "文件内容不一致";
    }

    /**
     * 设置列宽
     */
    private void setColumnWidths(Sheet sheet) {
        // A列：类型
        sheet.setColumnWidth(0, 15 * 256);
        // B列：文件路径（合并到G列）
        sheet.setColumnWidth(1, 50 * 256);
        sheet.setColumnWidth(2, 15 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 15 * 256);
        sheet.setColumnWidth(5, 15 * 256);
        sheet.setColumnWidth(6, 15 * 256);
        // H列：差异说明
        sheet.setColumnWidth(7, 25 * 256);
    }

    /**
     * 判断两个文件是否一致
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @param strategy 比较策略
     * @return true表示一致，false表示不一致
     */
    private boolean isFilesConsistent(FileInfoDto sourceFile, FileInfoDto targetFile, FileComparisonStrategy strategy) {
        if (sourceFile == null || targetFile == null) {
            return false;
        }

        // 根据策略进行比较
        if (strategy == FileComparisonStrategy.COMPREHENSIVE) {
            // 综合比较：文件大小、权限、MD5都要相同
            return StringUtils.equals(sourceFile.getFileSize(), targetFile.getFileSize()) &&
                   StringUtils.equals(sourceFile.getPermissions(), targetFile.getPermissions()) &&
                   StringUtils.equals(sourceFile.getMd5(), targetFile.getMd5());
        } else {
            // 仅MD5比较：只比较MD5值
            return StringUtils.equals(sourceFile.getMd5(), targetFile.getMd5());
        }
    }

    /**
     * 构建不一致文件的备注信息
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @param strategy 比较策略
     * @return 不一致备注信息
     */
    private String buildInconsistentRemark(FileInfoDto sourceFile, FileInfoDto targetFile, FileComparisonStrategy strategy) {
        if (strategy == FileComparisonStrategy.COMPREHENSIVE) {
            // 综合比较：详细说明哪些属性不一致
            StringBuilder remark = new StringBuilder("文件不一致：");
            boolean hasInconsistency = false;

            if (!StringUtils.equals(sourceFile.getFileSize(), targetFile.getFileSize())) {
                remark.append("文件大小不同");
                hasInconsistency = true;
            }

            if (!StringUtils.equals(sourceFile.getPermissions(), targetFile.getPermissions())) {
                if (hasInconsistency) {
                    remark.append("、");
                }
                remark.append("权限不同");
                hasInconsistency = true;
            }

            if (!StringUtils.equals(sourceFile.getMd5(), targetFile.getMd5())) {
                if (hasInconsistency) {
                    remark.append("、");
                }
                remark.append("MD5值不同");
            }

            return remark.toString();
        } else {
            // 仅MD5比较：只说明MD5不同
            return "文件内容不一致，MD5值不同";
        }
    }

}
