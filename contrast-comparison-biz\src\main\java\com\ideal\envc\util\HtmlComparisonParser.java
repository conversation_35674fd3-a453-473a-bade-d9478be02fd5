package com.ideal.envc.util;

import com.ideal.envc.model.dto.FileInfoDto;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * HTML比对结果解析器
 * 用于解析HTML格式的环境比对结果，提取源服务器和目标服务器的文件信息
 *
 * <AUTHOR>
 */
public class HtmlComparisonParser {
    
    private static final Logger logger = LoggerFactory.getLogger(HtmlComparisonParser.class);
    
    /**
     * 匹配比对行的正则表达式（备用方案）
     * 当dom4j解析失败时使用正则表达式解析
     */
    private static final Pattern COMPARISON_ROW_PATTERN = Pattern.compile(
        "<tr>\\s*" +
        "<td class=\"(cp_frame[^\"]*)\">.*?" +
        "<div class=\"cp_text\">(\\d+)</div>.*?" +
        "<div class=\"cp_cn\">([^<]+)</div>.*?" +
        "</td>.*?" +
        "<td class=\"(cp_frame[^\"]*)\">.*?" +
        "<div class=\"cp_text\">(\\d+)</div>.*?" +
        "<div class=\"cp_cn\">([^<]+)</div>.*?" +
        "</td>\\s*" +
        "</tr>",
        Pattern.DOTALL
    );
    
    /**
     * 文件信息解析正则表达式
     * 匹配格式：文件名 (size: 大小, permissions: 权限, MD5: MD5值)
     */
    private static final Pattern FILE_INFO_PATTERN = Pattern.compile(
        "^(.+?)\\s+\\(size:\\s*(.+?),\\s*permissions:\\s*(.+?),\\s*MD5:\\s*(.+?)\\)$"
    );
    
    /**
     * CSS类名到状态的映射
     */
    private static final String CLASS_CONSISTENT = "cp_frame cpi_td_w";           // 一致
    private static final String CLASS_INCONSISTENT = "cp_frame warning cpi_td_w"; // 不一致
    private static final String CLASS_MISSING = "cp_frame abnormal cpi_td_w";     // 缺失
    private static final String CLASS_EXTRA = "cp_frame complete cpi_td_w";       // 多出
    
    /**
     * 解析HTML比对结果
     *
     * @param htmlContent HTML内容
     * @return 解析结果
     */
    public static HtmlParseResult parseHtmlComparison(String htmlContent) {
        if (StringUtils.isBlank(htmlContent)) {
            logger.warn("HTML内容为空");
            return new HtmlParseResult();
        }

        HtmlParseResult result = new HtmlParseResult();
        List<ComparisonRow> rows = new ArrayList<>();

        try {
            // 首先尝试使用dom4j解析
            rows = parseWithDom4j(htmlContent);

            // 如果dom4j解析失败或结果为空，使用正则表达式作为备用方案
            if (rows.isEmpty()) {
                logger.warn("dom4j解析失败或无结果，使用正则表达式备用方案");
                rows = parseWithRegex(htmlContent);
            }

            result.setComparisonRows(rows);
            result.setTotalRows(rows.size());

            logger.info("HTML解析完成，共解析{}行比对数据", rows.size());

        } catch (Exception e) {
            logger.error("解析HTML比对结果时发生异常", e);
            throw new RuntimeException("HTML解析失败：" + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 使用dom4j解析HTML内容
     *
     * @param htmlContent HTML内容
     * @return 比对行列表
     */
    private static List<ComparisonRow> parseWithDom4j(String htmlContent) {
        List<ComparisonRow> rows = new ArrayList<>();

        try {
            // 预处理HTML内容，确保格式正确
            String processedHtml = preprocessHtml(htmlContent);

            // 解析HTML文档
            Document document = DocumentHelper.parseText(processedHtml);

            // 查找所有包含cp_frame类的td元素
            List<Element> tdElements = findComparisonElements(document);

            logger.info("找到{}个td元素", tdElements.size());

            // 调试：输出前几个td元素的详细信息
            debugTdElements(tdElements);

            // 分析HTML结构，判断是成对还是单独的td元素
            if (isTableRowStructure(tdElements)) {
                // 表格行结构：每个tr包含两个td（源和目标）
                logger.info("使用表格行结构解析");
                rows = parseTableRowStructure(tdElements);
            } else {
                // 单独td结构：每个td代表一行文本
                logger.info("使用单独td结构解析");
                rows = parseSingleTdStructure(tdElements);
            }

            logger.info("dom4j解析成功，解析到{}行数据", rows.size());

        } catch (Exception e) {
            logger.warn("dom4j解析失败：{}", e.getMessage());
        }

        return rows;
    }
    
    /**
     * 预处理HTML内容
     *
     * @param htmlContent 原始HTML内容
     * @return 处理后的HTML内容
     */
    private static String preprocessHtml(String htmlContent) {
        // 确保HTML有根元素
        if (!htmlContent.trim().startsWith("<html") && !htmlContent.trim().startsWith("<?xml")) {
            htmlContent = "<html><body>" + htmlContent + "</body></html>";
        }

        // 处理自闭合标签
        htmlContent = htmlContent.replaceAll("<(\\w+)([^>]*?)/>", "<$1$2></$1>");

        return htmlContent;
    }

    /**
     * 判断是否为表格行结构（每行包含两个td）
     */
    private static boolean isTableRowStructure(List<Element> tdElements) {
        if (tdElements.size() < 2) {
            return false;
        }

        // 检查前几个td元素是否成对出现在同一个tr中
        for (int i = 0; i < Math.min(4, tdElements.size() - 1); i += 2) {
            Element td1 = tdElements.get(i);
            Element td2 = tdElements.get(i + 1);

            // 检查是否在同一个tr中
            if (td1.getParent() != null && td1.getParent().equals(td2.getParent())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 解析表格行结构（每行包含两个td）
     */
    private static List<ComparisonRow> parseTableRowStructure(List<Element> tdElements) {
        List<ComparisonRow> rows = new ArrayList<>();

        // 按行分组处理
        for (int i = 0; i < tdElements.size(); i += 2) {
            if (i + 1 < tdElements.size()) {
                Element sourceTd = tdElements.get(i);
                Element targetTd = tdElements.get(i + 1);

                ComparisonRow row = parseComparisonRow(sourceTd, targetTd);
                if (row != null) {
                    rows.add(row);
                }
            }
        }

        return rows;
    }

    /**
     * 解析单独td结构（每个td代表一行文本）
     */
    private static List<ComparisonRow> parseSingleTdStructure(List<Element> tdElements) {
        List<ComparisonRow> rows = new ArrayList<>();

        for (Element td : tdElements) {
            ComparisonRow row = parseSingleTdRow(td);
            if (row != null) {
                rows.add(row);
            }
        }

        return rows;
    }

    /**
     * 解析单个td元素为比对行
     */
    private static ComparisonRow parseSingleTdRow(Element td) {
        try {
            String tdClass = td.attributeValue("class");
            Element textDiv = findElementByClass(td, "cp_text");
            Element cnDiv = findElementByClass(td, "cp_cn");

            if (textDiv == null || cnDiv == null) {
                return null;
            }

            String lineNum = textDiv.getTextTrim();
            String content = StringEscapeUtils.unescapeHtml4(cnDiv.getTextTrim());

            // 根据CSS类确定状态
            String status = determineStatusFromClass(tdClass);

            // 创建文件信息
            FileInfoDto fileInfo = parseFileInfo(content, lineNum);

            ComparisonRow row = new ComparisonRow();

            // 根据状态决定是源文件还是目标文件
            if ("缺失".equals(status)) {
                // 缺失：只有源文件，目标文件为空
                row.setSourceFile(fileInfo);
                row.setTargetFile(null);
                row.setSourceLineNum(Integer.parseInt(lineNum));
                row.setTargetLineNum(0);
            } else if ("多出".equals(status)) {
                // 多出：只有目标文件，源文件为空
                row.setSourceFile(null);
                row.setTargetFile(fileInfo);
                row.setSourceLineNum(0);
                row.setTargetLineNum(Integer.parseInt(lineNum));
            } else {
                // 一致或不一致：源和目标都有
                row.setSourceFile(fileInfo);
                row.setTargetFile(fileInfo);
                row.setSourceLineNum(Integer.parseInt(lineNum));
                row.setTargetLineNum(Integer.parseInt(lineNum));
            }

            row.setStatus(status);
            return row;

        } catch (Exception e) {
            logger.warn("解析单个td行失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析单个比对行
     *
     * @param sourceTd 源服务器td元素
     * @param targetTd 目标服务器td元素
     * @return 比对行对象
     */
    private static ComparisonRow parseComparisonRow(Element sourceTd, Element targetTd) {
        try {
            // 提取源服务器信息
            String sourceClass = sourceTd.attributeValue("class");
            Element sourceTextDiv = findElementByClass(sourceTd, "cp_text");
            Element sourceCnDiv = findElementByClass(sourceTd, "cp_cn");

            // 提取目标服务器信息
            String targetClass = targetTd.attributeValue("class");
            Element targetTextDiv = findElementByClass(targetTd, "cp_text");
            Element targetCnDiv = findElementByClass(targetTd, "cp_cn");

            if (sourceTextDiv == null || sourceCnDiv == null ||
                targetTextDiv == null || targetCnDiv == null) {
                return null;
            }

            String sourceLineNum = sourceTextDiv.getTextTrim();
            String sourceContent = StringEscapeUtils.unescapeHtml4(sourceCnDiv.getTextTrim());
            String targetLineNum = targetTextDiv.getTextTrim();
            String targetContent = StringEscapeUtils.unescapeHtml4(targetCnDiv.getTextTrim());

            // 解析文件信息
            FileInfoDto sourceFile = parseFileInfo(sourceContent, sourceLineNum);
            FileInfoDto targetFile = parseFileInfo(targetContent, targetLineNum);

            // 确定比对状态
            String status = determineStatus(sourceClass, targetClass, sourceFile, targetFile);

            ComparisonRow row = new ComparisonRow();
            row.setSourceFile(sourceFile);
            row.setTargetFile(targetFile);
            row.setStatus(status);

            // 安全解析行号，处理空值情况
            try {
                row.setSourceLineNum(StringUtils.isNotBlank(sourceLineNum) ? Integer.parseInt(sourceLineNum) : 0);
            } catch (NumberFormatException e) {
                row.setSourceLineNum(0);
            }

            try {
                row.setTargetLineNum(StringUtils.isNotBlank(targetLineNum) ? Integer.parseInt(targetLineNum) : 0);
            } catch (NumberFormatException e) {
                row.setTargetLineNum(0);
            }

            return row;

        } catch (Exception e) {
            logger.warn("解析比对行失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 使用正则表达式解析HTML内容（备用方案）
     *
     * @param htmlContent HTML内容
     * @return 比对行列表
     */
    private static List<ComparisonRow> parseWithRegex(String htmlContent) {
        List<ComparisonRow> rows = new ArrayList<>();

        try {
            Matcher matcher = COMPARISON_ROW_PATTERN.matcher(htmlContent);

            while (matcher.find()) {
                String sourceClass = matcher.group(1);
                String sourceLineNum = matcher.group(2);
                String sourceContent = StringEscapeUtils.unescapeHtml4(matcher.group(3));

                String targetClass = matcher.group(4);
                String targetLineNum = matcher.group(5);
                String targetContent = StringEscapeUtils.unescapeHtml4(matcher.group(6));

                // 解析文件信息
                FileInfoDto sourceFile = parseFileInfo(sourceContent, sourceLineNum);
                FileInfoDto targetFile = parseFileInfo(targetContent, targetLineNum);

                // 确定比对状态
                String status = determineStatus(sourceClass, targetClass, sourceFile, targetFile);

                ComparisonRow row = new ComparisonRow();
                row.setSourceFile(sourceFile);
                row.setTargetFile(targetFile);
                row.setStatus(status);
                row.setSourceLineNum(Integer.parseInt(sourceLineNum));
                row.setTargetLineNum(Integer.parseInt(targetLineNum));

                rows.add(row);

                logger.debug("正则解析比对行：源行号={}, 目标行号={}, 状态={}", sourceLineNum, targetLineNum, status);
            }

            logger.info("正则表达式解析完成，共解析{}行比对数据", rows.size());

        } catch (Exception e) {
            logger.error("正则表达式解析失败", e);
        }

        return rows;
    }

    /**
     * 解析文件信息字符串
     *
     * @param content 文件信息内容
     * @param lineNum 行号
     * @return 文件信息DTO
     */
    private static FileInfoDto parseFileInfo(String content, String lineNum) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        
        Matcher matcher = FILE_INFO_PATTERN.matcher(content.trim());
        
        if (matcher.matches()) {
            FileInfoDto fileInfo = new FileInfoDto();
            fileInfo.setFilePath(matcher.group(1).trim());
            fileInfo.setFileSize(matcher.group(2).trim());
            fileInfo.setPermissions(matcher.group(3).trim());
            fileInfo.setMd5(matcher.group(4).trim());
            return fileInfo;
        } else {
            // 如果不匹配标准格式，将整个内容作为文件路径
            logger.warn("文件信息格式不标准，行号：{}，内容：{}", lineNum, content);
            FileInfoDto fileInfo = new FileInfoDto();
            fileInfo.setFilePath(content.trim());
            fileInfo.setFileSize("");
            fileInfo.setPermissions("");
            fileInfo.setMd5("");
            return fileInfo;
        }
    }
    
    /**
     * 根据CSS类名确定比对状态
     *
     * @param sourceClass 源服务器CSS类名
     * @param targetClass 目标服务器CSS类名
     * @param sourceFile 源文件信息
     * @param targetFile 目标文件信息
     * @return 比对状态
     */
    private static String determineStatus(String sourceClass, String targetClass, 
                                        FileInfoDto sourceFile, FileInfoDto targetFile) {
        
        // 根据CSS类名判断状态
        if (CLASS_CONSISTENT.equals(sourceClass) && CLASS_CONSISTENT.equals(targetClass)) {
            return "一致";
        } else if (CLASS_INCONSISTENT.equals(sourceClass) && CLASS_INCONSISTENT.equals(targetClass)) {
            return "不一致";
        } else if (CLASS_MISSING.equals(sourceClass) && CLASS_CONSISTENT.equals(targetClass)) {
            // 缺失：基线有数据（abnormal），目标为空（cpi_td_w）
            return "缺失";
        } else if (CLASS_CONSISTENT.equals(sourceClass) && CLASS_EXTRA.equals(targetClass)) {
            // 多出：基线为空（cpi_td_w），目标有数据（complete）
            return "多出";
        } else {
            // 如果CSS类名不匹配预期，通过文件内容进一步判断
            boolean sourceEmpty = (sourceFile == null || StringUtils.isBlank(sourceFile.getFilePath()));
            boolean targetEmpty = (targetFile == null || StringUtils.isBlank(targetFile.getFilePath()));

            if (sourceEmpty && !targetEmpty) {
                return "多出";
            } else if (!sourceEmpty && targetEmpty) {
                return "缺失";
            } else if (!sourceEmpty && !targetEmpty) {
                // 比较MD5值
                if (StringUtils.isNotBlank(sourceFile.getMd5()) && 
                    StringUtils.isNotBlank(targetFile.getMd5())) {
                    return sourceFile.getMd5().equals(targetFile.getMd5()) ? "一致" : "不一致";
                } else {
                    // 如果没有MD5，比较文件路径
                    return sourceFile.getFilePath().equals(targetFile.getFilePath()) ? "一致" : "不一致";
                }
            }
        }
        
        return "未知";
    }

    /**
     * 根据CSS类确定状态
     */
    private static String determineStatusFromClass(String cssClass) {
        if (StringUtils.isBlank(cssClass)) {
            return "未知";
        }

        if (cssClass.contains("warning")) {
            return "不一致";
        } else if (cssClass.contains("abnormal")) {
            return "缺失";
        } else if (cssClass.contains("complete")) {
            return "多出";
        } else if (cssClass.contains("cp_frame")) {
            return "一致";
        }

        return "未知";
    }

    /**
     * 调试td元素信息
     */
    private static void debugTdElements(List<Element> tdElements) {
        logger.info("=== HTML解析调试信息 ===");
        for (int i = 0; i < Math.min(6, tdElements.size()); i++) {
            Element td = tdElements.get(i);
            String tdClass = td.attributeValue("class");
            Element textDiv = findElementByClass(td, "cp_text");
            Element cnDiv = findElementByClass(td, "cp_cn");

            logger.info("TD[{}] - class: {}", i, tdClass);
            if (textDiv != null) {
                logger.info("  cp_text: '{}'", textDiv.getTextTrim());
            } else {
                logger.info("  cp_text: null");
            }
            if (cnDiv != null) {
                logger.info("  cp_cn: '{}'", cnDiv.getTextTrim());
            } else {
                logger.info("  cp_cn: null");
            }

            // 输出td的完整HTML结构（截取前200字符）
            String tdHtml = td.asXML();
            if (tdHtml.length() > 200) {
                tdHtml = tdHtml.substring(0, 200) + "...";
            }
            logger.info("  HTML: {}", tdHtml);
            logger.info("  ---");
        }
        logger.info("=== 调试信息结束 ===");
    }
    
    /**
     * HTML解析结果类
     */
    public static class HtmlParseResult {
        private List<ComparisonRow> comparisonRows = new ArrayList<>();
        private int totalRows = 0;
        
        public List<ComparisonRow> getComparisonRows() {
            return comparisonRows;
        }
        
        public void setComparisonRows(List<ComparisonRow> comparisonRows) {
            this.comparisonRows = comparisonRows;
        }
        
        public int getTotalRows() {
            return totalRows;
        }
        
        public void setTotalRows(int totalRows) {
            this.totalRows = totalRows;
        }
    }
    
    /**
     * 比对行数据类
     */
    public static class ComparisonRow {
        private FileInfoDto sourceFile;
        private FileInfoDto targetFile;
        private String status;
        private int sourceLineNum;
        private int targetLineNum;
        
        public FileInfoDto getSourceFile() {
            return sourceFile;
        }
        
        public void setSourceFile(FileInfoDto sourceFile) {
            this.sourceFile = sourceFile;
        }
        
        public FileInfoDto getTargetFile() {
            return targetFile;
        }
        
        public void setTargetFile(FileInfoDto targetFile) {
            this.targetFile = targetFile;
        }
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
        
        public int getSourceLineNum() {
            return sourceLineNum;
        }
        
        public void setSourceLineNum(int sourceLineNum) {
            this.sourceLineNum = sourceLineNum;
        }
        
        public int getTargetLineNum() {
            return targetLineNum;
        }
        
        public void setTargetLineNum(int targetLineNum) {
            this.targetLineNum = targetLineNum;
        }
    }

    /**
     * 查找包含cp_frame类的td元素
     * 不使用XPath，避免jaxen依赖
     */
    private static List<Element> findComparisonElements(Document document) {
        List<Element> result = new ArrayList<>();
        Element root = document.getRootElement();
        findComparisonElementsRecursive(root, result);
        return result;
    }

    /**
     * 递归查找包含cp_frame类的td元素
     */
    private static void findComparisonElementsRecursive(Element element, List<Element> result) {
        if ("td".equals(element.getName())) {
            String classAttr = element.attributeValue("class");
            if (classAttr != null && classAttr.contains("cp_frame")) {
                result.add(element);
            }
        }

        // 递归处理子元素
        for (Element child : element.elements()) {
            findComparisonElementsRecursive(child, result);
        }
    }

    /**
     * 在指定元素下查找具有指定class的div元素
     * 不使用XPath，避免jaxen依赖
     */
    private static Element findElementByClass(Element parent, String className) {
        return findElementByClassRecursive(parent, className);
    }

    /**
     * 递归查找具有指定class的div元素
     */
    private static Element findElementByClassRecursive(Element element, String className) {
        if ("div".equals(element.getName())) {
            String classAttr = element.attributeValue("class");
            if (classAttr != null && classAttr.equals(className)) {
                return element;
            }
        }

        // 递归处理子元素
        for (Element child : element.elements()) {
            Element found = findElementByClassRecursive(child, className);
            if (found != null) {
                return found;
            }
        }

        return null;
    }
}
