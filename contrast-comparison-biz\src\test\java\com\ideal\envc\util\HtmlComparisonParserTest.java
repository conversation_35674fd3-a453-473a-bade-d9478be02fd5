package com.ideal.envc.util;

import com.ideal.envc.model.dto.FileInfoDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HtmlComparisonParser的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("HtmlComparisonParser单元测试")
class HtmlComparisonParserTest {

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 空内容")
    void testParseHtmlComparison_EmptyContent() {
        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison("");

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalRows());
        assertTrue(result.getComparisonRows().isEmpty());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - null内容")
    void testParseHtmlComparison_NullContent() {
        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalRows());
        assertTrue(result.getComparisonRows().isEmpty());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 空白内容")
    void testParseHtmlComparison_BlankContent() {
        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison("   ");

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalRows());
        assertTrue(result.getComparisonRows().isEmpty());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 简单HTML表格")
    void testParseHtmlComparison_SimpleHtmlTable() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertNotNull(row.getSourceFile());
        assertNotNull(row.getTargetFile());
        assertEquals("file1.txt", row.getSourceFile().getFilePath());
        assertEquals("file1.txt", row.getTargetFile().getFilePath());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 不一致文件")
    void testParseHtmlComparison_InconsistentFiles() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame warning cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 2048, permissions: -rw-r--r--, MD5: def456)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals("不一致", row.getStatus());
        assertEquals("1024", row.getSourceFile().getFileSize());
        assertEquals("2048", row.getTargetFile().getFileSize());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 缺失文件")
    void testParseHtmlComparison_MissingFiles() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame abnormal cpi_td_w\">" +
                "<div class=\"cp_text\">-</div>" +
                "<div class=\"cp_cn\">文件不存在</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals("不一致", row.getStatus());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 多出文件")
    void testParseHtmlComparison_ExtraFiles() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame abnormal cpi_td_w\">" +
                "<div class=\"cp_text\">-</div>" +
                "<div class=\"cp_cn\">文件不存在</div>" +
                "</td>" +
                "<td class=\"cp_frame complete cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals("不一致", row.getStatus());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 无根元素HTML")
    void testParseHtmlComparison_NoRootElement() {
        String htmlContent = "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        // 应该能够解析，因为会自动添加根元素
        assertTrue(result.getTotalRows() >= 0);
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 非标准文件信息格式")
    void testParseHtmlComparison_NonStandardFileFormat() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">简单文件名</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">简单文件名</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals("简单文件名", row.getSourceFile().getFilePath());
        assertEquals("", row.getSourceFile().getFileSize());
        assertEquals("", row.getSourceFile().getPermissions());
        assertEquals("", row.getSourceFile().getMd5());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 无效行号")
    void testParseHtmlComparison_InvalidLineNumber() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">abc</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">def</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals(0, row.getSourceLineNum()); // 无效行号应该被设置为0
        assertEquals(0, row.getTargetLineNum());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 空行号")
    void testParseHtmlComparison_EmptyLineNumber() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\"></div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\"></div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals(0, row.getSourceLineNum()); // 空行号应该被设置为0
        assertEquals(0, row.getTargetLineNum());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 复杂HTML结构")
    void testParseHtmlComparison_ComplexHtmlStructure() {
        String htmlContent = "<html><head><title>Test</title></head><body>" +
                "<div><p>Some text</p></div>" +
                "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame warning cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 2048, permissions: -rwxrwxrwx, MD5: def456)</div>" +
                "</td>" +
                "</tr>" +
                "<tr>" +
                "<td class=\"cp_frame abnormal cpi_td_w\">" +
                "<div class=\"cp_text\">2</div>" +
                "<div class=\"cp_cn\">file2.txt (size: 512, permissions: -rw-r--r--, MD5: ghi789)</div>" +
                "</td>" +
                "<td class=\"cp_frame abnormal cpi_td_w\">" +
                "<div class=\"cp_text\">-</div>" +
                "<div class=\"cp_cn\">文件不存在</div>" +
                "</td>" +
                "</tr>" +
                "</table>" +
                "<div><p>More text</p></div>" +
                "</body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotalRows());
        assertEquals(2, result.getComparisonRows().size());

        // 验证第一行
        HtmlComparisonParser.ComparisonRow row1 = result.getComparisonRows().get(0);
        assertEquals("不一致", row1.getStatus());
        assertEquals("file1.txt", row1.getSourceFile().getFilePath());
        assertEquals("1024", row1.getSourceFile().getFileSize());
        assertEquals("2048", row1.getTargetFile().getFileSize());

        // 验证第二行
        HtmlComparisonParser.ComparisonRow row2 = result.getComparisonRows().get(1);
        assertEquals("不一致", row2.getStatus());
        assertEquals("file2.txt", row2.getSourceFile().getFilePath());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 自闭合标签")
    void testParseHtmlComparison_SelfClosingTags() {
        String htmlContent = "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt<br/>size: 1024</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt<br/>size: 1024</div>" +
                "</td>" +
                "</tr>" +
                "</table>";

        // 执行测试方法 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);
            assertNotNull(result);
        });
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 异常HTML导致解析失败")
    void testParseHtmlComparison_ParseException() {
        // 使用严重格式错误的HTML
        String htmlContent = "<html><body><table><tr><td class=\"cp_frame cpi_td_w\"><div class=\"cp_text\">1</div><div class=\"cp_cn\">file1.txt</div></td><td class=\"cp_frame cpi_td_w\"><div class=\"cp_text\">1</div><div class=\"cp_cn\">file1.txt</div></td></tr></table></body></html>";

        // 执行测试方法 - 应该能够处理并返回结果
        assertDoesNotThrow(() -> {
            HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);
            assertNotNull(result);
        });
    }

    @Test
    @DisplayName("测试HtmlParseResult类")
    void testHtmlParseResult() {
        HtmlComparisonParser.HtmlParseResult result = new HtmlComparisonParser.HtmlParseResult();
        
        // 测试默认值
        assertNotNull(result.getComparisonRows());
        assertEquals(0, result.getTotalRows());
        
        // 测试设置值
        result.setTotalRows(5);
        assertEquals(5, result.getTotalRows());
        
        // 测试设置比对行
        List<HtmlComparisonParser.ComparisonRow> rows = new java.util.ArrayList<>();
        result.setComparisonRows(rows);
        assertEquals(rows, result.getComparisonRows());
    }

    @Test
    @DisplayName("测试ComparisonRow类")
    void testComparisonRow() {
        HtmlComparisonParser.ComparisonRow row = new HtmlComparisonParser.ComparisonRow();
        
        // 测试设置和获取源文件
        FileInfoDto sourceFile = new FileInfoDto();
        sourceFile.setFilePath("source.txt");
        row.setSourceFile(sourceFile);
        assertEquals(sourceFile, row.getSourceFile());
        
        // 测试设置和获取目标文件
        FileInfoDto targetFile = new FileInfoDto();
        targetFile.setFilePath("target.txt");
        row.setTargetFile(targetFile);
        assertEquals(targetFile, row.getTargetFile());
        
        // 测试设置和获取状态
        row.setStatus("一致");
        assertEquals("一致", row.getStatus());
        
        // 测试设置和获取行号
        row.setSourceLineNum(10);
        assertEquals(10, row.getSourceLineNum());
        
        row.setTargetLineNum(20);
        assertEquals(20, row.getTargetLineNum());
    }
}
