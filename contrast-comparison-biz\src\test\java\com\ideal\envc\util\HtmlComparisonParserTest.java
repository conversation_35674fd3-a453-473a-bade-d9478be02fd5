package com.ideal.envc.util;

import com.ideal.envc.model.dto.FileInfoDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HtmlComparisonParser的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("HtmlComparisonParser单元测试")
class HtmlComparisonParserTest {

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 空内容")
    void testParseHtmlComparison_EmptyContent() {
        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison("");

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalRows());
        assertTrue(result.getComparisonRows().isEmpty());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - null内容")
    void testParseHtmlComparison_NullContent() {
        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalRows());
        assertTrue(result.getComparisonRows().isEmpty());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 空白内容")
    void testParseHtmlComparison_BlankContent() {
        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison("   ");

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalRows());
        assertTrue(result.getComparisonRows().isEmpty());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 简单HTML表格")
    void testParseHtmlComparison_SimpleHtmlTable() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertNotNull(row.getSourceFile());
        assertNotNull(row.getTargetFile());
        assertEquals("file1.txt", row.getSourceFile().getFilePath());
        assertEquals("file1.txt", row.getTargetFile().getFilePath());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 不一致文件")
    void testParseHtmlComparison_InconsistentFiles() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame warning cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 2048, permissions: -rw-r--r--, MD5: def456)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals("不一致", row.getStatus());
        assertEquals("1024", row.getSourceFile().getFileSize());
        assertEquals("2048", row.getTargetFile().getFileSize());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 缺失文件")
    void testParseHtmlComparison_MissingFiles() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame abnormal cpi_td_w\">" +
                "<div class=\"cp_text\">-</div>" +
                "<div class=\"cp_cn\">文件不存在</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals("不一致", row.getStatus());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 多出文件")
    void testParseHtmlComparison_ExtraFiles() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame abnormal cpi_td_w\">" +
                "<div class=\"cp_text\">-</div>" +
                "<div class=\"cp_cn\">文件不存在</div>" +
                "</td>" +
                "<td class=\"cp_frame complete cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals("不一致", row.getStatus());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 无根元素HTML")
    void testParseHtmlComparison_NoRootElement() {
        String htmlContent = "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        // 应该能够解析，因为会自动添加根元素
        assertTrue(result.getTotalRows() >= 0);
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 非标准文件信息格式")
    void testParseHtmlComparison_NonStandardFileFormat() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">简单文件名</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">简单文件名</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals("简单文件名", row.getSourceFile().getFilePath());
        assertEquals("", row.getSourceFile().getFileSize());
        assertEquals("", row.getSourceFile().getPermissions());
        assertEquals("", row.getSourceFile().getMd5());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 无效行号")
    void testParseHtmlComparison_InvalidLineNumber() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">abc</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">def</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals(0, row.getSourceLineNum()); // 无效行号应该被设置为0
        assertEquals(0, row.getTargetLineNum());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 空行号")
    void testParseHtmlComparison_EmptyLineNumber() {
        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\"></div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\"></div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalRows());
        assertFalse(result.getComparisonRows().isEmpty());

        HtmlComparisonParser.ComparisonRow row = result.getComparisonRows().get(0);
        assertEquals(0, row.getSourceLineNum()); // 空行号应该被设置为0
        assertEquals(0, row.getTargetLineNum());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 复杂HTML结构")
    void testParseHtmlComparison_ComplexHtmlStructure() {
        String htmlContent = "<html><head><title>Test</title></head><body>" +
                "<div><p>Some text</p></div>" +
                "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame warning cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 2048, permissions: -rwxrwxrwx, MD5: def456)</div>" +
                "</td>" +
                "</tr>" +
                "<tr>" +
                "<td class=\"cp_frame abnormal cpi_td_w\">" +
                "<div class=\"cp_text\">2</div>" +
                "<div class=\"cp_cn\">file2.txt (size: 512, permissions: -rw-r--r--, MD5: ghi789)</div>" +
                "</td>" +
                "<td class=\"cp_frame abnormal cpi_td_w\">" +
                "<div class=\"cp_text\">-</div>" +
                "<div class=\"cp_cn\">文件不存在</div>" +
                "</td>" +
                "</tr>" +
                "</table>" +
                "<div><p>More text</p></div>" +
                "</body></html>";

        // 执行测试方法
        HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotalRows());
        assertEquals(2, result.getComparisonRows().size());

        // 验证第一行
        HtmlComparisonParser.ComparisonRow row1 = result.getComparisonRows().get(0);
        assertEquals("不一致", row1.getStatus());
        assertEquals("file1.txt", row1.getSourceFile().getFilePath());
        assertEquals("1024", row1.getSourceFile().getFileSize());
        assertEquals("2048", row1.getTargetFile().getFileSize());

        // 验证第二行
        HtmlComparisonParser.ComparisonRow row2 = result.getComparisonRows().get(1);
        assertEquals("不一致", row2.getStatus());
        assertEquals("file2.txt", row2.getSourceFile().getFilePath());
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 自闭合标签")
    void testParseHtmlComparison_SelfClosingTags() {
        String htmlContent = "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt<br/>size: 1024</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt<br/>size: 1024</div>" +
                "</td>" +
                "</tr>" +
                "</table>";

        // 执行测试方法 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);
            assertNotNull(result);
        });
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 异常HTML导致解析失败")
    void testParseHtmlComparison_ParseException() {
        // 使用严重格式错误的HTML
        String htmlContent = "<html><body><table><tr><td class=\"cp_frame cpi_td_w\"><div class=\"cp_text\">1</div><div class=\"cp_cn\">file1.txt</div></td><td class=\"cp_frame cpi_td_w\"><div class=\"cp_text\">1</div><div class=\"cp_cn\">file1.txt</div></td></tr></table></body></html>";

        // 执行测试方法 - 应该能够处理并返回结果
        assertDoesNotThrow(() -> {
            HtmlComparisonParser.HtmlParseResult result = HtmlComparisonParser.parseHtmlComparison(htmlContent);
            assertNotNull(result);
        });
    }

    @Test
    @DisplayName("测试HtmlParseResult类")
    void testHtmlParseResult() {
        HtmlComparisonParser.HtmlParseResult result = new HtmlComparisonParser.HtmlParseResult();
        
        // 测试默认值
        assertNotNull(result.getComparisonRows());
        assertEquals(0, result.getTotalRows());
        
        // 测试设置值
        result.setTotalRows(5);
        assertEquals(5, result.getTotalRows());
        
        // 测试设置比对行
        List<HtmlComparisonParser.ComparisonRow> rows = new java.util.ArrayList<>();
        result.setComparisonRows(rows);
        assertEquals(rows, result.getComparisonRows());
    }

    @Test
    @DisplayName("测试ComparisonRow类")
    void testComparisonRow() {
        HtmlComparisonParser.ComparisonRow row = new HtmlComparisonParser.ComparisonRow();
        
        // 测试设置和获取源文件
        FileInfoDto sourceFile = new FileInfoDto();
        sourceFile.setFilePath("source.txt");
        row.setSourceFile(sourceFile);
        assertEquals(sourceFile, row.getSourceFile());
        
        // 测试设置和获取目标文件
        FileInfoDto targetFile = new FileInfoDto();
        targetFile.setFilePath("target.txt");
        row.setTargetFile(targetFile);
        assertEquals(targetFile, row.getTargetFile());
        
        // 测试设置和获取状态
        row.setStatus("一致");
        assertEquals("一致", row.getStatus());
        
        // 测试设置和获取行号
        row.setSourceLineNum(10);
        assertEquals(10, row.getSourceLineNum());
        
        row.setTargetLineNum(20);
        assertEquals(20, row.getTargetLineNum());
    }

    @Test
    @DisplayName("测试parseWithDom4j方法 - cache场景：dom4j解析成功")
    void testParseWithDom4j_Success() throws Exception {
        // 使用反射调用private方法
        Method parseWithDom4jMethod = HtmlComparisonParser.class.getDeclaredMethod("parseWithDom4j", String.class);
        parseWithDom4jMethod.setAccessible(true);

        String htmlContent = "<html><body><table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: 755, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: 755, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table></body></html>";

        @SuppressWarnings("unchecked")
        List<HtmlComparisonParser.ComparisonRow> result = (List<HtmlComparisonParser.ComparisonRow>)
                parseWithDom4jMethod.invoke(null, htmlContent);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("一致", result.get(0).getStatus());
    }

    @Test
    @DisplayName("测试parseWithDom4j方法 - cache场景：dom4j解析失败")
    void testParseWithDom4j_Failure() throws Exception {
        // 使用反射调用private方法
        Method parseWithDom4jMethod = HtmlComparisonParser.class.getDeclaredMethod("parseWithDom4j", String.class);
        parseWithDom4jMethod.setAccessible(true);

        // 准备会导致解析失败的HTML
        String invalidHtml = "<invalid><xml>content</invalid>";

        @SuppressWarnings("unchecked")
        List<HtmlComparisonParser.ComparisonRow> result = (List<HtmlComparisonParser.ComparisonRow>)
                parseWithDom4jMethod.invoke(null, invalidHtml);

        // 解析失败时应该返回空列表
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    @DisplayName("测试preprocessHtml方法 - if判断分支")
    void testPreprocessHtml_IfBranches() throws Exception {
        // 使用反射调用private方法
        Method preprocessHtmlMethod = HtmlComparisonParser.class.getDeclaredMethod("preprocessHtml", String.class);
        preprocessHtmlMethod.setAccessible(true);

        // 测试已有html标签的情况
        String htmlWithTag = "<html><body>content</body></html>";
        String result1 = (String) preprocessHtmlMethod.invoke(null, htmlWithTag);
        assertEquals(htmlWithTag, result1);

        // 测试已有xml声明的情况
        String xmlContent = "<?xml version=\"1.0\"?><root>content</root>";
        String result2 = (String) preprocessHtmlMethod.invoke(null, xmlContent);
        assertEquals(xmlContent, result2);

        // 测试没有根元素的情况
        String noRootContent = "<div>content</div>";
        String result3 = (String) preprocessHtmlMethod.invoke(null, noRootContent);
        assertTrue(result3.startsWith("<html><body>"));
        assertTrue(result3.endsWith("</body></html>"));

        // 测试自闭合标签处理
        String selfClosingContent = "<input type=\"text\"/><br/>";
        String result4 = (String) preprocessHtmlMethod.invoke(null, selfClosingContent);
        assertTrue(result4.contains("<input type=\"text\"></input>"));
        assertTrue(result4.contains("<br></br>"));
    }

    @Test
    @DisplayName("测试isTableRowStructure方法 - for循环和if判断")
    void testIsTableRowStructure_ForLoopAndIfConditions() throws Exception {
        // 使用反射调用private方法
        Method isTableRowStructureMethod = HtmlComparisonParser.class.getDeclaredMethod("isTableRowStructure", List.class);
        isTableRowStructureMethod.setAccessible(true);

        // 测试空列表
        List<org.dom4j.Element> emptyList = new java.util.ArrayList<>();
        Boolean result1 = (Boolean) isTableRowStructureMethod.invoke(null, emptyList);
        assertFalse(result1);

        // 测试只有一个元素的列表
        List<org.dom4j.Element> singleElementList = new java.util.ArrayList<>();
        org.dom4j.Document doc = org.dom4j.DocumentHelper.parseText("<html><body><table><tr><td class=\"cp_frame\">test</td></tr></table></body></html>");
        org.dom4j.Element td = (org.dom4j.Element) doc.selectSingleNode("//td");
        singleElementList.add(td);
        Boolean result2 = (Boolean) isTableRowStructureMethod.invoke(null, singleElementList);
        assertFalse(result2);

        // 测试两个在同一tr中的td元素
        String htmlContent = "<html><body><table><tr><td class=\"cp_frame\">td1</td><td class=\"cp_frame\">td2</td></tr></table></body></html>";
        org.dom4j.Document doc2 = org.dom4j.DocumentHelper.parseText(htmlContent);
        List<org.dom4j.Element> tdElements = doc2.selectNodes("//td[@class='cp_frame']");
        Boolean result3 = (Boolean) isTableRowStructureMethod.invoke(null, tdElements);
        assertTrue(result3);

        // 测试两个不在同一tr中的td元素
        String htmlContent2 = "<html><body><table><tr><td class=\"cp_frame\">td1</td></tr><tr><td class=\"cp_frame\">td2</td></tr></table></body></html>";
        org.dom4j.Document doc3 = org.dom4j.DocumentHelper.parseText(htmlContent2);
        List<org.dom4j.Element> tdElements2 = doc3.selectNodes("//td[@class='cp_frame']");
        Boolean result4 = (Boolean) isTableRowStructureMethod.invoke(null, tdElements2);
        assertFalse(result4);
    }

    @Test
    @DisplayName("测试parseTableRowStructure方法 - if判断")
    void testParseTableRowStructure_IfConditions() throws Exception {
        // 使用反射调用private方法
        Method parseTableRowStructureMethod = HtmlComparisonParser.class.getDeclaredMethod("parseTableRowStructure", List.class);
        parseTableRowStructureMethod.setAccessible(true);

        // 准备测试数据 - 奇数个td元素
        String htmlContent = "<html><body><table>" +
                "<tr><td class=\"cp_frame cpi_td_w\"><div class=\"cp_text\">1</div><div class=\"cp_cn\">file1.txt</div></td>" +
                "<td class=\"cp_frame cpi_td_w\"><div class=\"cp_text\">1</div><div class=\"cp_cn\">file1.txt</div></td></tr>" +
                "<tr><td class=\"cp_frame cpi_td_w\"><div class=\"cp_text\">2</div><div class=\"cp_cn\">file2.txt</div></td></tr>" +
                "</table></body></html>";

        org.dom4j.Document doc = org.dom4j.DocumentHelper.parseText(htmlContent);
        List<org.dom4j.Element> tdElements = doc.selectNodes("//td[@class='cp_frame cpi_td_w']");

        @SuppressWarnings("unchecked")
        List<HtmlComparisonParser.ComparisonRow> result = (List<HtmlComparisonParser.ComparisonRow>)
                parseTableRowStructureMethod.invoke(null, tdElements);

        // 应该只解析成对的td元素，奇数个会被忽略
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    @DisplayName("测试parseSingleTdStructure方法 - 全方法覆盖")
    void testParseSingleTdStructure_FullMethod() throws Exception {
        // 使用反射调用private方法
        Method parseSingleTdStructureMethod = HtmlComparisonParser.class.getDeclaredMethod("parseSingleTdStructure", List.class);
        parseSingleTdStructureMethod.setAccessible(true);

        // 准备测试数据
        String htmlContent = "<html><body><table>" +
                "<tr><td class=\"cp_frame cpi_td_w\"><div class=\"cp_text\">1</div><div class=\"cp_cn\">file1.txt</div></td></tr>" +
                "<tr><td class=\"cp_frame warning cpi_td_w\"><div class=\"cp_text\">2</div><div class=\"cp_cn\">file2.txt</div></td></tr>" +
                "<tr><td class=\"cp_frame abnormal cpi_td_w\"><div class=\"cp_text\">3</div><div class=\"cp_cn\">file3.txt</div></td></tr>" +
                "<tr><td class=\"cp_frame complete cpi_td_w\"><div class=\"cp_text\">4</div><div class=\"cp_cn\">file4.txt</div></td></tr>" +
                "</table></body></html>";

        org.dom4j.Document doc = org.dom4j.DocumentHelper.parseText(htmlContent);
        List<org.dom4j.Element> tdElements = doc.selectNodes("//td[contains(@class, 'cp_frame')]");

        @SuppressWarnings("unchecked")
        List<HtmlComparisonParser.ComparisonRow> result = (List<HtmlComparisonParser.ComparisonRow>)
                parseSingleTdStructureMethod.invoke(null, tdElements);

        assertNotNull(result);
        assertEquals(4, result.size());

        // 验证不同状态的处理
        assertEquals("一致", result.get(0).getStatus());
        assertEquals("不一致", result.get(1).getStatus());
        assertEquals("缺失", result.get(2).getStatus());
        assertEquals("多出", result.get(3).getStatus());
    }

    @Test
    @DisplayName("测试parseSingleTdRow方法 - 全方法覆盖")
    void testParseSingleTdRow_FullMethod() throws Exception {
        // 使用反射调用private方法
        Method parseSingleTdRowMethod = HtmlComparisonParser.class.getDeclaredMethod("parseSingleTdRow", org.dom4j.Element.class);
        parseSingleTdRowMethod.setAccessible(true);

        // 测试缺失文件场景
        String missingHtml = "<td class=\"cp_frame abnormal cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">missing_file.txt</div>" +
                "</td>";
        org.dom4j.Document missingDoc = org.dom4j.DocumentHelper.parseText("<root>" + missingHtml + "</root>");
        org.dom4j.Element missingTd = (org.dom4j.Element) missingDoc.selectSingleNode("//td");

        HtmlComparisonParser.ComparisonRow missingResult = (HtmlComparisonParser.ComparisonRow)
                parseSingleTdRowMethod.invoke(null, missingTd);

        assertNotNull(missingResult);
        assertEquals("缺失", missingResult.getStatus());
        assertNotNull(missingResult.getSourceFile());
        assertNull(missingResult.getTargetFile());
        assertEquals(1, missingResult.getSourceLineNum());
        assertEquals(0, missingResult.getTargetLineNum());

        // 测试多出文件场景
        String extraHtml = "<td class=\"cp_frame complete cpi_td_w\">" +
                "<div class=\"cp_text\">2</div>" +
                "<div class=\"cp_cn\">extra_file.txt</div>" +
                "</td>";
        org.dom4j.Document extraDoc = org.dom4j.DocumentHelper.parseText("<root>" + extraHtml + "</root>");
        org.dom4j.Element extraTd = (org.dom4j.Element) extraDoc.selectSingleNode("//td");

        HtmlComparisonParser.ComparisonRow extraResult = (HtmlComparisonParser.ComparisonRow)
                parseSingleTdRowMethod.invoke(null, extraTd);

        assertNotNull(extraResult);
        assertEquals("多出", extraResult.getStatus());
        assertNull(extraResult.getSourceFile());
        assertNotNull(extraResult.getTargetFile());
        assertEquals(0, extraResult.getSourceLineNum());
        assertEquals(2, extraResult.getTargetLineNum());

        // 测试一致文件场景
        String consistentHtml = "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">3</div>" +
                "<div class=\"cp_cn\">consistent_file.txt</div>" +
                "</td>";
        org.dom4j.Document consistentDoc = org.dom4j.DocumentHelper.parseText("<root>" + consistentHtml + "</root>");
        org.dom4j.Element consistentTd = (org.dom4j.Element) consistentDoc.selectSingleNode("//td");

        HtmlComparisonParser.ComparisonRow consistentResult = (HtmlComparisonParser.ComparisonRow)
                parseSingleTdRowMethod.invoke(null, consistentTd);

        assertNotNull(consistentResult);
        assertEquals("一致", consistentResult.getStatus());
        assertNotNull(consistentResult.getSourceFile());
        assertNotNull(consistentResult.getTargetFile());
        assertEquals(3, consistentResult.getSourceLineNum());
        assertEquals(3, consistentResult.getTargetLineNum());

        // 测试缺少必要元素的情况
        String incompleteHtml = "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">4</div>" +
                "</td>";
        org.dom4j.Document incompleteDoc = org.dom4j.DocumentHelper.parseText("<root>" + incompleteHtml + "</root>");
        org.dom4j.Element incompleteTd = (org.dom4j.Element) incompleteDoc.selectSingleNode("//td");

        HtmlComparisonParser.ComparisonRow incompleteResult = (HtmlComparisonParser.ComparisonRow)
                parseSingleTdRowMethod.invoke(null, incompleteTd);

        assertNull(incompleteResult); // 缺少必要元素应该返回null
    }

    @Test
    @DisplayName("测试parseComparisonRow方法 - if判断和cache场景")
    void testParseComparisonRow_IfConditionsAndCacheScenarios() throws Exception {
        // 使用反射调用private方法
        Method parseComparisonRowMethod = HtmlComparisonParser.class.getDeclaredMethod(
                "parseComparisonRow", org.dom4j.Element.class, org.dom4j.Element.class);
        parseComparisonRowMethod.setAccessible(true);

        // 测试正常情况
        String normalHtml = "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: 755, MD5: abc123)</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt (size: 1024, permissions: 755, MD5: abc123)</div>" +
                "</td>" +
                "</tr>" +
                "</table>";
        org.dom4j.Document normalDoc = org.dom4j.DocumentHelper.parseText(normalHtml);
        List<org.dom4j.Element> normalTds = normalDoc.selectNodes("//td");

        HtmlComparisonParser.ComparisonRow normalResult = (HtmlComparisonParser.ComparisonRow)
                parseComparisonRowMethod.invoke(null, normalTds.get(0), normalTds.get(1));

        assertNotNull(normalResult);
        assertEquals("一致", normalResult.getStatus());
        assertEquals(1, normalResult.getSourceLineNum());
        assertEquals(1, normalResult.getTargetLineNum());

        // 测试缺少必要元素的情况（sourceTextDiv为null）
        String missingSourceTextHtml = "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_cn\">file1.txt</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt</div>" +
                "</td>" +
                "</tr>" +
                "</table>";
        org.dom4j.Document missingDoc = org.dom4j.DocumentHelper.parseText(missingSourceTextHtml);
        List<org.dom4j.Element> missingTds = missingDoc.selectNodes("//td");

        HtmlComparisonParser.ComparisonRow missingResult = (HtmlComparisonParser.ComparisonRow)
                parseComparisonRowMethod.invoke(null, missingTds.get(0), missingTds.get(1));

        assertNull(missingResult); // 缺少必要元素应该返回null

        // 测试行号解析异常的情况
        String invalidLineNumHtml = "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">abc</div>" +
                "<div class=\"cp_cn\">file1.txt</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">def</div>" +
                "<div class=\"cp_cn\">file1.txt</div>" +
                "</td>" +
                "</tr>" +
                "</table>";
        org.dom4j.Document invalidDoc = org.dom4j.DocumentHelper.parseText(invalidLineNumHtml);
        List<org.dom4j.Element> invalidTds = invalidDoc.selectNodes("//td");

        HtmlComparisonParser.ComparisonRow invalidResult = (HtmlComparisonParser.ComparisonRow)
                parseComparisonRowMethod.invoke(null, invalidTds.get(0), invalidTds.get(1));

        assertNotNull(invalidResult);
        assertEquals(0, invalidResult.getSourceLineNum()); // 无效行号应该被设置为0
        assertEquals(0, invalidResult.getTargetLineNum());

        // 测试空行号的情况
        String emptyLineNumHtml = "<table>" +
                "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\"></div>" +
                "<div class=\"cp_cn\">file1.txt</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\"></div>" +
                "<div class=\"cp_cn\">file1.txt</div>" +
                "</td>" +
                "</tr>" +
                "</table>";
        org.dom4j.Document emptyDoc = org.dom4j.DocumentHelper.parseText(emptyLineNumHtml);
        List<org.dom4j.Element> emptyTds = emptyDoc.selectNodes("//td");

        HtmlComparisonParser.ComparisonRow emptyResult = (HtmlComparisonParser.ComparisonRow)
                parseComparisonRowMethod.invoke(null, emptyTds.get(0), emptyTds.get(1));

        assertNotNull(emptyResult);
        assertEquals(0, emptyResult.getSourceLineNum()); // 空行号应该被设置为0
        assertEquals(0, emptyResult.getTargetLineNum());
    }

    @Test
    @DisplayName("测试parseWithRegex方法 - 全方法覆盖")
    void testParseWithRegex_FullMethod() throws Exception {
        // 使用反射调用private方法
        Method parseWithRegexMethod = HtmlComparisonParser.class.getDeclaredMethod("parseWithRegex", String.class);
        parseWithRegexMethod.setAccessible(true);

        // 准备符合正则表达式的HTML内容
        String regexHtml = "<tr>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt</div>" +
                "</td>" +
                "<td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt</div>" +
                "</td>" +
                "</tr>" +
                "<tr>" +
                "<td class=\"cp_frame warning cpi_td_w\">" +
                "<div class=\"cp_text\">2</div>" +
                "<div class=\"cp_cn\">file2.txt</div>" +
                "</td>" +
                "<td class=\"cp_frame warning cpi_td_w\">" +
                "<div class=\"cp_text\">2</div>" +
                "<div class=\"cp_cn\">file2_modified.txt</div>" +
                "</td>" +
                "</tr>";

        @SuppressWarnings("unchecked")
        List<HtmlComparisonParser.ComparisonRow> result = (List<HtmlComparisonParser.ComparisonRow>)
                parseWithRegexMethod.invoke(null, regexHtml);

        assertNotNull(result);
        assertTrue(result.size() >= 0); // 正则表达式可能匹配到内容

        // 测试不匹配正则表达式的HTML
        String nonMatchingHtml = "<div>This does not match the regex pattern</div>";

        @SuppressWarnings("unchecked")
        List<HtmlComparisonParser.ComparisonRow> emptyResult = (List<HtmlComparisonParser.ComparisonRow>)
                parseWithRegexMethod.invoke(null, nonMatchingHtml);

        assertNotNull(emptyResult);
        assertEquals(0, emptyResult.size());

        // 测试会导致异常的情况
        String problematicHtml = null;

        @SuppressWarnings("unchecked")
        List<HtmlComparisonParser.ComparisonRow> exceptionResult = (List<HtmlComparisonParser.ComparisonRow>)
                parseWithRegexMethod.invoke(null, problematicHtml);

        assertNotNull(exceptionResult);
        assertEquals(0, exceptionResult.size()); // 异常时应该返回空列表
    }

    @Test
    @DisplayName("测试parseFileInfo方法 - if判断")
    void testParseFileInfo_IfConditions() throws Exception {
        // 使用反射调用private方法
        Method parseFileInfoMethod = HtmlComparisonParser.class.getDeclaredMethod("parseFileInfo", String.class, String.class);
        parseFileInfoMethod.setAccessible(true);

        // 测试空内容
        FileInfoDto result1 = (FileInfoDto) parseFileInfoMethod.invoke(null, "", "1");
        assertNull(result1);

        FileInfoDto result2 = (FileInfoDto) parseFileInfoMethod.invoke(null, null, "1");
        assertNull(result2);

        FileInfoDto result3 = (FileInfoDto) parseFileInfoMethod.invoke(null, "   ", "1");
        assertNull(result3);

        // 测试标准格式
        String standardFormat = "file.txt (size: 1024, permissions: 755, MD5: abc123)";
        FileInfoDto result4 = (FileInfoDto) parseFileInfoMethod.invoke(null, standardFormat, "1");
        assertNotNull(result4);
        assertEquals("file.txt", result4.getFilePath());
        assertEquals("1024", result4.getFileSize());
        assertEquals("755", result4.getPermissions());
        assertEquals("abc123", result4.getMd5());

        // 测试非标准格式
        String nonStandardFormat = "simple_file.txt";
        FileInfoDto result5 = (FileInfoDto) parseFileInfoMethod.invoke(null, nonStandardFormat, "2");
        assertNotNull(result5);
        assertEquals("simple_file.txt", result5.getFilePath());
        assertEquals("", result5.getFileSize());
        assertEquals("", result5.getPermissions());
        assertEquals("", result5.getMd5());
    }

    @Test
    @DisplayName("测试determineStatus方法 - 全方法覆盖")
    void testDetermineStatus_FullMethod() throws Exception {
        // 使用反射调用private方法
        Method determineStatusMethod = HtmlComparisonParser.class.getDeclaredMethod(
                "determineStatus", String.class, String.class, FileInfoDto.class, FileInfoDto.class);
        determineStatusMethod.setAccessible(true);

        // 准备测试数据
        FileInfoDto sourceFile = new FileInfoDto();
        sourceFile.setFilePath("file.txt");
        sourceFile.setMd5("abc123");

        FileInfoDto targetFile = new FileInfoDto();
        targetFile.setFilePath("file.txt");
        targetFile.setMd5("abc123");

        FileInfoDto differentTargetFile = new FileInfoDto();
        differentTargetFile.setFilePath("file_modified.txt");
        differentTargetFile.setMd5("def456");

        // 测试一致情况
        String result1 = (String) determineStatusMethod.invoke(null,
                "cp_frame cpi_td_w", "cp_frame cpi_td_w", sourceFile, targetFile);
        assertEquals("一致", result1);

        // 测试不一致情况
        String result2 = (String) determineStatusMethod.invoke(null,
                "cp_frame warning cpi_td_w", "cp_frame warning cpi_td_w", sourceFile, differentTargetFile);
        assertEquals("不一致", result2);

        // 测试缺失情况
        String result3 = (String) determineStatusMethod.invoke(null,
                "cp_frame abnormal cpi_td_w", "cp_frame cpi_td_w", sourceFile, targetFile);
        assertEquals("缺失", result3);

        // 测试多出情况
        String result4 = (String) determineStatusMethod.invoke(null,
                "cp_frame cpi_td_w", "cp_frame complete cpi_td_w", sourceFile, targetFile);
        assertEquals("多出", result4);

        // 测试通过文件内容判断 - 源为空，目标不为空（多出）
        String result5 = (String) determineStatusMethod.invoke(null,
                "unknown_class", "unknown_class", null, targetFile);
        assertEquals("多出", result5);

        // 测试通过文件内容判断 - 源不为空，目标为空（缺失）
        String result6 = (String) determineStatusMethod.invoke(null,
                "unknown_class", "unknown_class", sourceFile, null);
        assertEquals("缺失", result6);

        // 测试通过MD5判断 - MD5相同（一致）
        String result7 = (String) determineStatusMethod.invoke(null,
                "unknown_class", "unknown_class", sourceFile, targetFile);
        assertEquals("一致", result7);

        // 测试通过MD5判断 - MD5不同（不一致）
        String result8 = (String) determineStatusMethod.invoke(null,
                "unknown_class", "unknown_class", sourceFile, differentTargetFile);
        assertEquals("不一致", result8);

        // 测试没有MD5时通过文件路径判断
        FileInfoDto sourceFileNoMd5 = new FileInfoDto();
        sourceFileNoMd5.setFilePath("file.txt");
        sourceFileNoMd5.setMd5("");

        FileInfoDto targetFileNoMd5 = new FileInfoDto();
        targetFileNoMd5.setFilePath("file.txt");
        targetFileNoMd5.setMd5("");

        String result9 = (String) determineStatusMethod.invoke(null,
                "unknown_class", "unknown_class", sourceFileNoMd5, targetFileNoMd5);
        assertEquals("一致", result9);

        // 测试文件路径不同
        FileInfoDto differentTargetFileNoMd5 = new FileInfoDto();
        differentTargetFileNoMd5.setFilePath("different_file.txt");
        differentTargetFileNoMd5.setMd5("");

        String result10 = (String) determineStatusMethod.invoke(null,
                "unknown_class", "unknown_class", sourceFileNoMd5, differentTargetFileNoMd5);
        assertEquals("不一致", result10);

        // 测试未知情况
        String result11 = (String) determineStatusMethod.invoke(null,
                "unknown_class", "unknown_class", null, null);
        assertEquals("未知", result11);
    }

    @Test
    @DisplayName("测试determineStatusFromClass方法 - 全方法覆盖")
    void testDetermineStatusFromClass_FullMethod() throws Exception {
        // 使用反射调用private方法
        Method determineStatusFromClassMethod = HtmlComparisonParser.class.getDeclaredMethod("determineStatusFromClass", String.class);
        determineStatusFromClassMethod.setAccessible(true);

        // 测试空或null的CSS类
        String result1 = (String) determineStatusFromClassMethod.invoke(null, (String) null);
        assertEquals("未知", result1);

        String result2 = (String) determineStatusFromClassMethod.invoke(null, "");
        assertEquals("未知", result2);

        String result3 = (String) determineStatusFromClassMethod.invoke(null, "   ");
        assertEquals("未知", result3);

        // 测试包含warning的类
        String result4 = (String) determineStatusFromClassMethod.invoke(null, "cp_frame warning cpi_td_w");
        assertEquals("不一致", result4);

        // 测试包含abnormal的类
        String result5 = (String) determineStatusFromClassMethod.invoke(null, "cp_frame abnormal cpi_td_w");
        assertEquals("缺失", result5);

        // 测试包含complete的类
        String result6 = (String) determineStatusFromClassMethod.invoke(null, "cp_frame complete cpi_td_w");
        assertEquals("多出", result6);

        // 测试包含cp_frame的类（一致）
        String result7 = (String) determineStatusFromClassMethod.invoke(null, "cp_frame cpi_td_w");
        assertEquals("一致", result7);

        // 测试不包含任何已知类的情况
        String result8 = (String) determineStatusFromClassMethod.invoke(null, "unknown_class");
        assertEquals("未知", result8);
    }

    @Test
    @DisplayName("测试debugTdElements方法 - if判断")
    void testDebugTdElements_IfConditions() throws Exception {
        // 使用反射调用private方法
        Method debugTdElementsMethod = HtmlComparisonParser.class.getDeclaredMethod("debugTdElements", List.class);
        debugTdElementsMethod.setAccessible(true);

        // 准备测试数据
        String htmlContent = "<html><body><table>" +
                "<tr><td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">1</div>" +
                "<div class=\"cp_cn\">file1.txt</div>" +
                "</td></tr>" +
                "<tr><td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_text\">2</div>" +
                "</td></tr>" + // 缺少cp_cn div
                "<tr><td class=\"cp_frame cpi_td_w\">" +
                "<div class=\"cp_cn\">file3.txt</div>" +
                "</td></tr>" + // 缺少cp_text div
                "</table></body></html>";

        org.dom4j.Document doc = org.dom4j.DocumentHelper.parseText(htmlContent);
        List<org.dom4j.Element> tdElements = doc.selectNodes("//td");

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            debugTdElementsMethod.invoke(null, tdElements);
        });

        // 测试空列表
        List<org.dom4j.Element> emptyList = new java.util.ArrayList<>();
        assertDoesNotThrow(() -> {
            debugTdElementsMethod.invoke(null, emptyList);
        });

        // 测试超过6个元素的列表（测试Math.min逻辑）
        String largeHtmlContent = "<html><body><table>";
        for (int i = 1; i <= 10; i++) {
            largeHtmlContent += "<tr><td class=\"cp_frame cpi_td_w\">" +
                    "<div class=\"cp_text\">" + i + "</div>" +
                    "<div class=\"cp_cn\">file" + i + ".txt</div>" +
                    "</td></tr>";
        }
        largeHtmlContent += "</table></body></html>";

        org.dom4j.Document largeDoc = org.dom4j.DocumentHelper.parseText(largeHtmlContent);
        List<org.dom4j.Element> largeTdElements = largeDoc.selectNodes("//td");

        assertDoesNotThrow(() -> {
            debugTdElementsMethod.invoke(null, largeTdElements);
        });
    }

    @Test
    @DisplayName("测试findComparisonElementsRecursive方法 - 里层if判断")
    void testFindComparisonElementsRecursive_InnerIfConditions() throws Exception {
        // 使用反射调用private方法
        Method findComparisonElementsRecursiveMethod = HtmlComparisonParser.class.getDeclaredMethod(
                "findComparisonElementsRecursive", org.dom4j.Element.class, List.class);
        findComparisonElementsRecursiveMethod.setAccessible(true);

        // 准备测试数据
        String htmlContent = "<html><body>" +
                "<div>" +
                "<td class=\"cp_frame cpi_td_w\">td with cp_frame</td>" +
                "<td class=\"other_class\">td without cp_frame</td>" +
                "<td>td without class</td>" +
                "<span class=\"cp_frame\">span with cp_frame (not td)</span>" +
                "</div>" +
                "</body></html>";

        org.dom4j.Document doc = org.dom4j.DocumentHelper.parseText(htmlContent);
        org.dom4j.Element root = doc.getRootElement();
        List<org.dom4j.Element> result = new java.util.ArrayList<>();

        // 执行测试
        findComparisonElementsRecursiveMethod.invoke(null, root, result);

        // 验证结果 - 只有td元素且包含cp_frame类的才会被添加
        assertEquals(1, result.size());
        assertEquals("td", result.get(0).getName());
        assertTrue(result.get(0).attributeValue("class").contains("cp_frame"));
    }

    @Test
    @DisplayName("测试findElementByClassRecursive方法 - 里层if判断")
    void testFindElementByClassRecursive_InnerIfConditions() throws Exception {
        // 使用反射调用private方法
        Method findElementByClassRecursiveMethod = HtmlComparisonParser.class.getDeclaredMethod(
                "findElementByClassRecursive", org.dom4j.Element.class, String.class);
        findElementByClassRecursiveMethod.setAccessible(true);

        // 准备测试数据
        String htmlContent = "<html><body>" +
                "<div>" +
                "<div class=\"cp_text\">target div</div>" +
                "<div class=\"other_class\">other div</div>" +
                "<span class=\"cp_text\">span with cp_text (not div)</span>" +
                "<div>div without class</div>" +
                "<div class=\"cp_text nested\">" +
                "<div class=\"cp_text\">nested target</div>" +
                "</div>" +
                "</div>" +
                "</body></html>";

        org.dom4j.Document doc = org.dom4j.DocumentHelper.parseText(htmlContent);
        org.dom4j.Element root = doc.getRootElement();

        // 测试找到第一个匹配的元素
        org.dom4j.Element result1 = (org.dom4j.Element) findElementByClassRecursiveMethod.invoke(null, root, "cp_text");
        assertNotNull(result1);
        assertEquals("div", result1.getName());
        assertEquals("cp_text", result1.attributeValue("class"));
        assertEquals("target div", result1.getTextTrim());

        // 测试找不到匹配元素
        org.dom4j.Element result2 = (org.dom4j.Element) findElementByClassRecursiveMethod.invoke(null, root, "nonexistent_class");
        assertNull(result2);

        // 测试在没有div元素的情况下
        String noDivHtml = "<html><body><span class=\"cp_text\">span only</span></body></html>";
        org.dom4j.Document noDivDoc = org.dom4j.DocumentHelper.parseText(noDivHtml);
        org.dom4j.Element noDivRoot = noDivDoc.getRootElement();

        org.dom4j.Element result3 = (org.dom4j.Element) findElementByClassRecursiveMethod.invoke(null, noDivRoot, "cp_text");
        assertNull(result3); // 只有div元素才会被匹配
    }
}
