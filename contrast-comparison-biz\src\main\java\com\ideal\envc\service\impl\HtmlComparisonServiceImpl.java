package com.ideal.envc.service.impl;

import com.alibaba.fastjson.JSON;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.bean.RunFlowDetailBean;
import com.ideal.envc.model.dto.ContentCustomDto;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.FileComparisonResultDto;
import com.ideal.envc.model.dto.FileInfoDto;
import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;
import com.ideal.envc.model.dto.TextLineComparisonDto;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import com.ideal.envc.service.IFileComparisonService;
import com.ideal.envc.service.IHtmlComparisonService;
import com.ideal.envc.util.HtmlComparisonParser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HTML比对服务实现类
 *
 * <AUTHOR>
 */
@Service
public class HtmlComparisonServiceImpl implements IHtmlComparisonService {
    
    private static final Logger logger = LoggerFactory.getLogger(HtmlComparisonServiceImpl.class);

    private final IFileComparisonService fileComparisonService;
    private final RunFlowResultMapper runFlowResultMapper;
    private final RunRuleMapper runRuleMapper;

    public HtmlComparisonServiceImpl(IFileComparisonService fileComparisonService,
                                   RunFlowResultMapper runFlowResultMapper,
                                   RunRuleMapper runRuleMapper) {
        this.fileComparisonService = fileComparisonService;
        this.runFlowResultMapper = runFlowResultMapper;
        this.runRuleMapper = runRuleMapper;
    }
    
    @Override
    public HtmlComparisonResultDto parseHtmlComparison(HtmlComparisonRequestDto request) throws ContrastBusinessException {
        logger.info("开始解析HTML比对内容，flowId：{}，基线服务器：{}，目标服务器：{}",
                request.getFlowId(), request.getBaselineServer(), request.getTargetServer());

        // 验证输入参数
        if (request.getFlowId() == null) {
            throw new ContrastBusinessException("flowId不能为空");
        }

        try {
            // 查询流程结果和详情
            RunFlowResultEntity runFlowResult = queryFlowResult(request.getFlowId());
            RunFlowDetailBean runFlowDetailBean = queryFlowDetail(request.getFlowId());

            // 解析内容
            String content = getFlowContent(runFlowResult, request.getFlowId());
            ContentCustomDto contentCustomDto = JSON.parseObject(content, ContentCustomDto.class);

            if (contentCustomDto == null) {
                throw new ContrastBusinessException("解析流程内容失败，内容格式不正确");
            }

            // 获取真正的HTML内容
            String htmlContent = contentCustomDto.getContent();
            if (StringUtils.isBlank(htmlContent)) {
                throw new ContrastBusinessException("流程内容中的HTML内容为空");
            }

            // 使用流程详情补充请求信息
            enrichRequestFromFlowDetail(request, runFlowDetailBean);

            // 检查是否为HTML格式还是直接的文件列表格式
            if (isHtmlFormat(htmlContent)) {
                // 使用HTML解析器解析内容
                HtmlComparisonParser.HtmlParseResult parseResult =
                    HtmlComparisonParser.parseHtmlComparison(htmlContent);

                // 转换为文件比对结果
                HtmlComparisonResultDto result = convertToComparisonResult(parseResult, request);

                logger.info("HTML解析完成，结果：{}", result);
                return result;
            } else {
                // 直接使用现有的文件比对服务处理
                return parseDirectFileContent(request, contentCustomDto);
            }

        } catch (Exception e) {
            logger.error("HTML比对解析过程中发生异常", e);
            throw new ContrastBusinessException("HTML比对解析失败：" + e.getMessage());
        }
    }

    /**
     * 判断内容是否为HTML格式
     *
     * @param content 内容字符串
     * @return 是否为HTML格式
     */
    private boolean isHtmlFormat(String content) {
        return content.trim().startsWith("<") &&
               (content.contains("<div") || content.contains("<table") || content.contains("<tr"));
    }

    /**
     * 查询流程结果
     *
     * @param flowId 流程ID
     * @return 流程结果实体
     */
    private RunFlowResultEntity queryFlowResult(Long flowId) throws ContrastBusinessException {
        RunFlowResultEntity runFlowResult = runFlowResultMapper.selectRunFlowResultByFlowId(flowId);
        if (runFlowResult == null) {
            logger.warn("根据flowId未查询到流程结果数据，flowId：{}", flowId);
            throw new ContrastBusinessException("未查询到对应的流程结果数据");
        }
        return runFlowResult;
    }

    /**
     * 查询流程详情
     *
     * @param flowId 流程ID
     * @return 流程详情Bean
     */
    private RunFlowDetailBean queryFlowDetail(Long flowId) throws ContrastBusinessException {
        List<RunFlowDetailBean> runFlowDetailBeanList = runRuleMapper.selectRunRuleDetailByFlowId(flowId);
        if (runFlowDetailBeanList == null || runFlowDetailBeanList.isEmpty()) {
            throw new ContrastBusinessException("根据流程ID未查询到对应设备相关信息");
        }
        return runFlowDetailBeanList.get(0);
    }

    /**
     * 获取流程内容
     *
     * @param runFlowResult 流程结果实体
     * @param flowId 流程ID
     * @return 流程内容
     */
    private String getFlowContent(RunFlowResultEntity runFlowResult, Long flowId) throws ContrastBusinessException {
        String content = runFlowResult.getContent();
        if (StringUtils.isBlank(content)) {
            content = runFlowResult.getStderr();
        }

        if (StringUtils.isBlank(content)) {
            logger.warn("流程结果内容为空，flowId：{}", flowId);
            throw new ContrastBusinessException("流程结果内容为空，无法进行比对解析");
        }
        return content;
    }

    /**
     * 使用流程详情补充请求信息
     *
     * @param request 请求对象
     * @param runFlowDetailBean 流程详情
     */
    private void enrichRequestFromFlowDetail(HtmlComparisonRequestDto request, RunFlowDetailBean runFlowDetailBean) {
        // 补充服务器信息
        if (StringUtils.isBlank(request.getBaselineServer())) {
            request.setBaselineServer(runFlowDetailBean.getSourceComputerName());
        }
        if (StringUtils.isBlank(request.getTargetServer())) {
            request.setTargetServer(runFlowDetailBean.getTargetComputerName());
        }

        // 补充IP信息
        if (StringUtils.isBlank(request.getBaseServerIp())) {
            request.setBaseServerIp(runFlowDetailBean.getSourceComputerIp());
        }
        if (StringUtils.isBlank(request.getTargetServerIp())) {
            request.setTargetServerIp(runFlowDetailBean.getTargetComputerIp());
        }

        // 补充其他信息
        request.setSourcePath(runFlowDetailBean.getSourcePath());
        request.setPath(runFlowDetailBean.getPath());
        request.setBusinessSystemId(runFlowDetailBean.getBusinessSystemId());
        request.setSourceComputerId(runFlowDetailBean.getSourceComputerId());
        request.setTargetComputerId(runFlowDetailBean.getTargetComputerId());
        request.setTargetCenterName(runFlowDetailBean.getTargetCenterName());
        request.setSourceCenterName(runFlowDetailBean.getSourceCenterName());
        request.setBusinessSystemName(runFlowDetailBean.getBusinessSystemName());

        // 如果没有描述，生成默认描述
        if (StringUtils.isBlank(request.getDescription())) {
            request.setDescription(String.format("%s与%s环境比对",
                    runFlowDetailBean.getSourceCenterName(),
                    runFlowDetailBean.getTargetCenterName()));
        }
    }

    /**
     * 解析直接的文件内容格式
     *
     * @param request 请求参数
     * @param contentCustomDto 内容自定义DTO
     * @return 比对结果
     */
    private HtmlComparisonResultDto parseDirectFileContent(HtmlComparisonRequestDto request,
                                                         ContentCustomDto contentCustomDto) throws ContrastBusinessException {
        logger.info("解析直接文件内容格式");

        try {
            String sourceContent = contentCustomDto.getSourceContent();
            String targetContent = contentCustomDto.getTargetContent();

            if (StringUtils.isBlank(sourceContent) && StringUtils.isBlank(targetContent)) {
                throw new ContrastBusinessException("源内容和目标内容都为空，无法进行比对");
            }

            // 使用现有的文件比对服务
            FileComparisonRequestDto fileRequest = new FileComparisonRequestDto();
            fileRequest.setSourceContent(sourceContent);
            fileRequest.setTargetContent(targetContent);
            fileRequest.setBaselineServer(request.getBaselineServer());
            fileRequest.setTargetServer(request.getTargetServer());
            fileRequest.setBaseServerIp(request.getBaseServerIp());
            fileRequest.setTargetServerIp(request.getTargetServerIp());
            fileRequest.setDescription(request.getDescription());
            fileRequest.setFlowId(request.getFlowId());
            fileRequest.setSourcePath(request.getSourcePath());
            fileRequest.setPath(request.getPath());
            fileRequest.setBusinessSystemId(request.getBusinessSystemId());
            fileRequest.setSourceComputerId(request.getSourceComputerId());
            fileRequest.setTargetComputerId(request.getTargetComputerId());

            FileComparisonResultDto fileResult = fileComparisonService.compareFileContents(fileRequest);

            // 转换为HTML比对结果
            return convertFileResultToHtmlResult(fileResult, request);

        } catch (Exception e) {
            logger.error("解析直接文件内容失败", e);
            throw new ContrastBusinessException("解析文件内容失败：" + e.getMessage());
        }
    }
    
    @Override
    public void exportHtmlComparisonResult(HtmlComparisonRequestDto request, HttpServletResponse response) 
            throws ContrastBusinessException {
        HtmlComparisonResultDto result = parseHtmlComparison(request);
        exportHtmlComparisonResult(request, result, response);
    }
    
    @Override
    public void exportHtmlComparisonResult(HtmlComparisonRequestDto request, HtmlComparisonResultDto result,
                                         HttpServletResponse response) throws ContrastBusinessException {
        try {
            logger.info("开始导出HTML比对结果，flowId：{}", request.getFlowId());

            // 设置响应头
            String fileName = "环境一致性比对结果_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));

            // 使用专门的HTML比对Excel导出方法
            exportHtmlComparisonWithPoi(response.getOutputStream(), result, request);

            logger.info("HTML比对结果导出完成，flowId：{}", request.getFlowId());

        } catch (Exception e) {
            logger.error("HTML比对结果导出失败", e);
            throw new ContrastBusinessException("HTML比对结果导出失败：" + e.getMessage());
        }
    }
    
    /**
     * 将HTML解析结果转换为比对结果DTO
     *
     * @param parseResult HTML解析结果
     * @param request 请求参数
     * @return 比对结果DTO
     */
    private HtmlComparisonResultDto convertToComparisonResult(HtmlComparisonParser.HtmlParseResult parseResult,
                                                            HtmlComparisonRequestDto request) {
        HtmlComparisonResultDto result = new HtmlComparisonResultDto();

        // 设置基本信息
        setBasicInfo(result, request, parseResult);

        // 分类统计文件
        FileClassificationResult classification = classifyFiles(parseResult);

        // 设置文件列表
        setFileListsToResult(result, classification);

        // 计算并设置统计信息
        calculateAndSetStatistics(result, classification, parseResult);

        return result;
    }

    /**
     * 设置基本信息
     *
     * @param result 结果对象
     * @param request 请求参数
     * @param parseResult 解析结果
     */
    private void setBasicInfo(HtmlComparisonResultDto result, HtmlComparisonRequestDto request,
                             HtmlComparisonParser.HtmlParseResult parseResult) {
        result.setBaselineServer(StringUtils.defaultIfBlank(request.getBaselineServer(), "源服务器"));
        result.setTargetServer(StringUtils.defaultIfBlank(request.getTargetServer(), "目标服务器"));
        result.setDescription(request.getDescription());
        result.setBaseServerIp(request.getBaseServerIp());
        result.setTargetServerIp(request.getTargetServerIp());
        result.setTotalHtmlRows(parseResult.getTotalRows());
    }

    /**
     * 文件分类结果
     */
    private static class FileClassificationResult {
        private final List<FileInfoDto> consistentFiles = new ArrayList<>();
        private final List<FileInfoDto> inconsistentFiles = new ArrayList<>();
        private final List<FileInfoDto> missingFiles = new ArrayList<>();
        private final List<FileInfoDto> extraFiles = new ArrayList<>();
        private final Map<String, FileInfoDto> sourceFileMap = new HashMap<>();
        private final Map<String, FileInfoDto> targetFileMap = new HashMap<>();

        public List<FileInfoDto> getConsistentFiles() { return consistentFiles; }
        public List<FileInfoDto> getInconsistentFiles() { return inconsistentFiles; }
        public List<FileInfoDto> getMissingFiles() { return missingFiles; }
        public List<FileInfoDto> getExtraFiles() { return extraFiles; }
        public Map<String, FileInfoDto> getSourceFileMap() { return sourceFileMap; }
        public Map<String, FileInfoDto> getTargetFileMap() { return targetFileMap; }
    }

    /**
     * 分类文件
     *
     * @param parseResult 解析结果
     * @return 文件分类结果
     */
    private FileClassificationResult classifyFiles(HtmlComparisonParser.HtmlParseResult parseResult) {
        FileClassificationResult classification = new FileClassificationResult();

        // 处理解析结果
        for (HtmlComparisonParser.ComparisonRow row : parseResult.getComparisonRows()) {
            processComparisonRow(row, classification);
        }

        return classification;
    }

    /**
     * 处理比对行
     *
     * @param row 比对行
     * @param classification 分类结果
     */
    private void processComparisonRow(HtmlComparisonParser.ComparisonRow row, FileClassificationResult classification) {
        FileInfoDto sourceFile = row.getSourceFile();
        FileInfoDto targetFile = row.getTargetFile();
        String status = row.getStatus();
        int sourceLineNum = row.getSourceLineNum();
        int targetLineNum = row.getTargetLineNum();

        // 根据状态分类
        switch (status) {
            case "一致":
                processConsistentFiles(sourceFile, targetFile, sourceLineNum, targetLineNum, classification);
                break;
            case "不一致":
                processInconsistentFiles(sourceFile, targetFile, sourceLineNum, targetLineNum, classification);
                break;
            case "缺失":
                processMissingFiles(sourceFile, sourceLineNum, classification);
                break;
            case "多出":
                processExtraFiles(targetFile, targetLineNum, classification);
                break;
            default:
                logger.warn("未知的比对状态：{}", status);
                break;
        }
    }

    /**
     * 处理一致文件
     */
    private void processConsistentFiles(FileInfoDto sourceFile, FileInfoDto targetFile, int sourceLineNum,
                                       int targetLineNum, FileClassificationResult classification) {
        if (sourceFile != null) {
            sourceFile.setFilePath(sourceLineNum + ":" + sourceFile.getFilePath());
            sourceFile.setStatus("一致");
            sourceFile.setRemark("文件一致");
            classification.getConsistentFiles().add(sourceFile);
            classification.getSourceFileMap().put(sourceFile.getFilePath(), sourceFile);
        }
        if (targetFile != null) {
            targetFile.setFilePath(targetLineNum + ":" + targetFile.getFilePath());
            classification.getTargetFileMap().put(targetFile.getFilePath(), targetFile);
        }
    }

    /**
     * 处理不一致文件
     */
    private void processInconsistentFiles(FileInfoDto sourceFile, FileInfoDto targetFile, int sourceLineNum,
                                         int targetLineNum, FileClassificationResult classification) {
        if (sourceFile != null && targetFile != null) {
            // 创建一个包含基线和目标内容的特殊FileInfoDto
            FileInfoDto inconsistentFile = new FileInfoDto();
            inconsistentFile.setFilePath(sourceLineNum + ":" + sourceFile.getFilePath());
            inconsistentFile.setFileSize(sourceFile.getFileSize());
            inconsistentFile.setPermissions(sourceFile.getPermissions());
            inconsistentFile.setMd5(sourceFile.getMd5());
            inconsistentFile.setStatus("不一致");

            // 在remark中保存目标内容，格式为："目标内容:实际目标内容"
            String targetContent = targetFile.getFilePath();
            inconsistentFile.setRemark("目标内容:" + targetContent);

            classification.getInconsistentFiles().add(inconsistentFile);
            classification.getSourceFileMap().put(inconsistentFile.getFilePath(), inconsistentFile);

            // 同时保存目标文件信息
            targetFile.setFilePath(targetLineNum + ":" + targetFile.getFilePath());
            classification.getTargetFileMap().put(targetFile.getFilePath(), targetFile);
        }
    }

    /**
     * 处理缺失文件
     */
    private void processMissingFiles(FileInfoDto sourceFile, int sourceLineNum, FileClassificationResult classification) {
        if (sourceFile != null) {
            sourceFile.setFilePath(sourceLineNum + ":" + sourceFile.getFilePath());
            sourceFile.setStatus("缺失");
            sourceFile.setRemark("目标服务器中不存在此文件");
            classification.getMissingFiles().add(sourceFile);
            classification.getSourceFileMap().put(sourceFile.getFilePath(), sourceFile);
        }
    }

    /**
     * 处理多出文件
     */
    private void processExtraFiles(FileInfoDto targetFile, int targetLineNum, FileClassificationResult classification) {
        if (targetFile != null) {
            targetFile.setFilePath(targetLineNum + ":" + targetFile.getFilePath());
            targetFile.setStatus("多出");
            targetFile.setRemark("基线服务器中不存在此文件");
            classification.getExtraFiles().add(targetFile);
            classification.getTargetFileMap().put(targetFile.getFilePath(), targetFile);
        }
    }

    /**
     * 设置文件列表到结果对象
     */
    private void setFileListsToResult(HtmlComparisonResultDto result, FileClassificationResult classification) {
        result.setConsistentFiles(classification.getConsistentFiles());
        result.setInconsistentFiles(classification.getInconsistentFiles());
        result.setMissingFiles(classification.getMissingFiles());
        result.setExtraFiles(classification.getExtraFiles());
    }

    /**
     * 计算并设置统计信息
     */
    private void calculateAndSetStatistics(HtmlComparisonResultDto result, FileClassificationResult classification,
                                          HtmlComparisonParser.HtmlParseResult parseResult) {
        int consistentCount = classification.getConsistentFiles().size();
        int inconsistentCount = classification.getInconsistentFiles().size();
        int missingCount = classification.getMissingFiles().size();
        int extraCount = classification.getExtraFiles().size();

        // 计算最大行号
        int maxLineNumber = calculateMaxLineNumber(parseResult);

        // 根据新的计算规则：
        // 基线汇总行数 = 最大行号 - 多出行数
        // 目标汇总行数 = 最大行号 - 缺失行数
        int totalSourceFiles = maxLineNumber - extraCount;
        int totalTargetFiles = maxLineNumber - missingCount;

        result.setTotalSourceFiles(totalSourceFiles);
        result.setTotalTargetFiles(totalTargetFiles);
        result.setConsistentCount(consistentCount);
        result.setInconsistentCount(inconsistentCount);
        result.setMissingCount(missingCount);
        result.setExtraCount(extraCount);

        // 计算比率
        result.setConsistentRate(calculateRate(consistentCount, totalSourceFiles));
        result.setInconsistentRate(calculateRate(inconsistentCount, totalSourceFiles));
        result.setMissingRate(calculateRate(missingCount, totalSourceFiles));
        result.setExtraRate(calculateRate(extraCount, totalTargetFiles));
    }

    /**
     * 计算最大行号
     */
    private int calculateMaxLineNumber(HtmlComparisonParser.HtmlParseResult parseResult) {
        int maxLineNumber = 0;
        for (HtmlComparisonParser.ComparisonRow row : parseResult.getComparisonRows()) {
            maxLineNumber = Math.max(maxLineNumber, Math.max(row.getSourceLineNum(), row.getTargetLineNum()));
        }
        return maxLineNumber;
    }

    /**
     * 构建不一致文件的备注信息
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @return 备注信息
     */
    private String buildInconsistentRemark(FileInfoDto sourceFile, FileInfoDto targetFile) {
        if (sourceFile == null || targetFile == null) {
            return "文件信息不完整";
        }
        
        List<String> differences = new ArrayList<>();
        
        if (!StringUtils.equals(sourceFile.getFileSize(), targetFile.getFileSize())) {
            differences.add("大小不同");
        }
        
        if (!StringUtils.equals(sourceFile.getPermissions(), targetFile.getPermissions())) {
            differences.add("权限不同");
        }
        
        if (!StringUtils.equals(sourceFile.getMd5(), targetFile.getMd5())) {
            differences.add("MD5不同");
        }
        
        return differences.isEmpty() ? "文件不一致" : String.join("、", differences);
    }
    
    /**
     * 计算比率
     *
     * @param count 数量
     * @param total 总数
     * @return 比率
     */
    private BigDecimal calculateRate(int count, int total) {
        if (total == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(count)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);
    }
    
    /**
     * 转换为FileComparisonRequestDto
     *
     * @param request HTML比对请求
     * @param result HTML比对结果
     * @return 文件比对请求
     */
    private FileComparisonRequestDto convertToFileComparisonRequest(HtmlComparisonRequestDto request, 
                                                                  HtmlComparisonResultDto result) {
        FileComparisonRequestDto fileRequest = new FileComparisonRequestDto();
        
        // 构建源内容和目标内容字符串
        StringBuilder sourceContent = new StringBuilder();
        StringBuilder targetContent = new StringBuilder();
        
        // 添加一致文件
        for (FileInfoDto file : result.getConsistentFiles()) {
            sourceContent.append(formatFileInfo(file)).append("\n");
            targetContent.append(formatFileInfo(file)).append("\n");
        }
        
        // 添加不一致文件
        for (FileInfoDto file : result.getInconsistentFiles()) {
            sourceContent.append(formatFileInfo(file)).append("\n");
            // 目标文件信息需要从原始数据中获取，这里简化处理
            targetContent.append(formatFileInfo(file)).append("\n");
        }
        
        // 添加缺失文件（只在源内容中）
        for (FileInfoDto file : result.getMissingFiles()) {
            sourceContent.append(formatFileInfo(file)).append("\n");
        }
        
        // 添加多出文件（只在目标内容中）
        for (FileInfoDto file : result.getExtraFiles()) {
            targetContent.append(formatFileInfo(file)).append("\n");
        }
        
        fileRequest.setSourceContent(sourceContent.toString());
        fileRequest.setTargetContent(targetContent.toString());
        fileRequest.setBaselineServer(request.getBaselineServer());
        fileRequest.setTargetServer(request.getTargetServer());
        fileRequest.setBaseServerIp(request.getBaseServerIp());
        fileRequest.setTargetServerIp(request.getTargetServerIp());
        fileRequest.setDescription(request.getDescription());
        fileRequest.setFlowId(request.getFlowId());
        
        return fileRequest;
    }
    
    /**
     * 转换为FileComparisonResultDto
     *
     * @param result HTML比对结果
     * @return 文件比对结果
     */
    private FileComparisonResultDto convertToFileComparisonResult(HtmlComparisonResultDto result) {
        FileComparisonResultDto fileResult = new FileComparisonResultDto();
        
        fileResult.setBaselineServer(result.getBaselineServer());
        fileResult.setTargetServer(result.getTargetServer());
        fileResult.setDescription(result.getDescription());
        fileResult.setTotalSourceFiles(result.getTotalSourceFiles());
        fileResult.setTotalTargetFiles(result.getTotalTargetFiles());
        fileResult.setConsistentCount(result.getConsistentCount());
        fileResult.setInconsistentCount(result.getInconsistentCount());
        fileResult.setMissingCount(result.getMissingCount());
        fileResult.setExtraCount(result.getExtraCount());
        fileResult.setConsistentRate(result.getConsistentRate());
        fileResult.setInconsistentRate(result.getInconsistentRate());
        fileResult.setMissingRate(result.getMissingRate());
        fileResult.setExtraRate(result.getExtraRate());
        fileResult.setConsistentFiles(result.getConsistentFiles());
        fileResult.setInconsistentFiles(result.getInconsistentFiles());
        fileResult.setMissingFiles(result.getMissingFiles());
        fileResult.setExtraFiles(result.getExtraFiles());
        
        return fileResult;
    }
    
    /**
     * 将文件比对结果转换为HTML比对结果
     *
     * @param fileResult 文件比对结果
     * @param request 请求参数
     * @return HTML比对结果
     */
    private HtmlComparisonResultDto convertFileResultToHtmlResult(FileComparisonResultDto fileResult,
                                                                HtmlComparisonRequestDto request) {
        HtmlComparisonResultDto result = new HtmlComparisonResultDto();

        // 复制基本信息
        result.setBaselineServer(fileResult.getBaselineServer());
        result.setTargetServer(fileResult.getTargetServer());
        result.setDescription(fileResult.getDescription());
        result.setBaseServerIp(request.getBaseServerIp());
        result.setTargetServerIp(request.getTargetServerIp());

        // 复制统计信息
        result.setTotalSourceFiles(fileResult.getTotalSourceFiles());
        result.setTotalTargetFiles(fileResult.getTotalTargetFiles());
        result.setConsistentCount(fileResult.getConsistentCount());
        result.setInconsistentCount(fileResult.getInconsistentCount());
        result.setMissingCount(fileResult.getMissingCount());
        result.setExtraCount(fileResult.getExtraCount());
        result.setConsistentRate(fileResult.getConsistentRate());
        result.setInconsistentRate(fileResult.getInconsistentRate());
        result.setMissingRate(fileResult.getMissingRate());
        result.setExtraRate(fileResult.getExtraRate());

        // 复制文件列表
        result.setConsistentFiles(fileResult.getConsistentFiles());
        result.setInconsistentFiles(fileResult.getInconsistentFiles());
        result.setMissingFiles(fileResult.getMissingFiles());
        result.setExtraFiles(fileResult.getExtraFiles());

        // 设置HTML特有的信息
        result.setTotalHtmlRows(0); // 非HTML格式，设置为0

        return result;
    }

    /**
     * 使用POI导出HTML比对结果Excel
     *
     * @param outputStream 输出流
     * @param result HTML比对结果
     * @param request 请求参数
     * @throws IOException IO异常
     */
    private void exportHtmlComparisonWithPoi(OutputStream outputStream, HtmlComparisonResultDto result,
                                             HtmlComparisonRequestDto request) throws IOException {
        Workbook workbook = new XSSFWorkbook();

        try {
            // 创建样式
            Map<String, CellStyle> styles = createHtmlComparisonStyles(workbook);

            // 创建主Sheet
            Sheet sheet = workbook.createSheet("环境一致性比对结果");

            // 创建Excel内容
            createHtmlComparisonSheet(sheet, result, request, styles);

            // 写入输出流
            workbook.write(outputStream);

        } finally {
            workbook.close();
        }
    }

    /**
     * 创建HTML比对专用样式
     *
     * @param workbook 工作簿
     * @return 样式映射
     */
    private Map<String, CellStyle> createHtmlComparisonStyles(Workbook workbook) {
        Map<String, CellStyle> styles = new HashMap<>();

        // 创建基础样式
        styles.put("title", createTitleStyle(workbook));
        styles.put("description", createDescriptionStyle(workbook));
        styles.put("header", createHeaderStyle(workbook));
        styles.put("data", createDataStyle(workbook));

        // 创建文件状态样式
        styles.put("inconsistent", createInconsistentStyle(workbook));
        styles.put("consistent", createConsistentStyle(workbook));
        styles.put("missing", createMissingStyle(workbook));
        styles.put("extra", createExtraStyle(workbook));

        return styles;
    }

    /**
     * 创建标题样式
     */
    private CellStyle createTitleStyle(Workbook workbook) {
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 12);
        titleFont.setBold(true);
        titleFont.setColor(IndexedColors.BLACK.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.LEFT);
        titleStyle.setVerticalAlignment(VerticalAlignment.TOP); // 改为顶部对齐，适配自动换行
        titleStyle.setWrapText(true); // 统一设置自动换行
        return titleStyle;
    }

    /**
     * 创建说明文字样式
     */
    private CellStyle createDescriptionStyle(Workbook workbook) {
        CellStyle descStyle = workbook.createCellStyle();
        Font descFont = workbook.createFont();
        descFont.setFontName("宋体");
        descFont.setFontHeightInPoints((short) 10);
        descFont.setBold(true);
        descFont.setColor(IndexedColors.RED.getIndex());
        descStyle.setFont(descFont);
        descStyle.setAlignment(HorizontalAlignment.LEFT);
        descStyle.setVerticalAlignment(VerticalAlignment.TOP); // 改为顶部对齐，适配自动换行
        descStyle.setWrapText(true); // 统一设置自动换行
        return descStyle;
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setFontName("宋体");
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.TOP); // 改为顶部对齐，适配自动换行
        headerStyle.setFillForegroundColor(IndexedColors.BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setWrapText(true); // 统一设置自动换行
        setBorders(headerStyle);
        return headerStyle;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        Font dataFont = workbook.createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.TOP); // 改为顶部对齐，适配自动换行
        dataStyle.setWrapText(true); // 统一设置自动换行
        setBorders(dataStyle);
        return dataStyle;
    }

    /**
     * 创建不一致文件样式
     */
    private CellStyle createInconsistentStyle(Workbook workbook) {
        return createFileStatusStyle(workbook, IndexedColors.LIGHT_ORANGE.getIndex());
    }

    /**
     * 创建一致文件样式
     */
    private CellStyle createConsistentStyle(Workbook workbook) {
        return createFileStatusStyle(workbook, IndexedColors.LIGHT_GREEN.getIndex());
    }

    /**
     * 创建缺失文件样式
     */
    private CellStyle createMissingStyle(Workbook workbook) {
        return createFileStatusStyle(workbook, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
    }

    /**
     * 创建多出文件样式
     */
    private CellStyle createExtraStyle(Workbook workbook) {
        return createFileStatusStyle(workbook, IndexedColors.LIGHT_YELLOW.getIndex());
    }

    /**
     * 创建文件状态样式的通用方法
     */
    private CellStyle createFileStatusStyle(Workbook workbook, short backgroundColor) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.TOP); // 改为顶部对齐，适配自动换行
        style.setFillForegroundColor(backgroundColor);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setWrapText(true); // 统一设置自动换行
        setBorders(style);
        return style;
    }



    /**
     * 创建HTML比对结果Sheet
     *
     * @param sheet Excel工作表
     * @param result 比对结果
     * @param request 请求参数
     * @param styles 样式映射
     */
    private void createHtmlComparisonSheet(Sheet sheet, HtmlComparisonResultDto result,
                                         HtmlComparisonRequestDto request, Map<String, CellStyle> styles) {
        int rowIndex = 0;

        // 1. 创建标题区域
        rowIndex = createTitleSection(sheet, result, request, styles, rowIndex);

        // 2. 创建说明区域
        rowIndex = createDescriptionSection(sheet, styles, rowIndex);

        // 3. 创建汇总统计表格
        rowIndex = createSummaryTable(sheet, result, request, styles, rowIndex);

        // 4. 创建详细文件列表
        rowIndex = createDetailFileList(sheet, result, styles, rowIndex);

        // 5. 设置列宽
        setColumnWidths(sheet);
    }

    /**
     * 创建标题区域
     *
     * @param sheet Excel工作表
     * @param result 比对结果
     * @param request 请求参数
     * @param styles 样式映射
     * @param startRow 起始行
     * @return 下一行索引
     */
    private int createTitleSection(Sheet sheet, HtmlComparisonResultDto result, HtmlComparisonRequestDto request,
                                 Map<String, CellStyle> styles, int startRow) {
        int rowIndex = startRow;

        // 标题：环境一致性比对结果报告
        Row titleRow = sheet.createRow(rowIndex++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("环境一致性比对结果报告");
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), 0, 7));

        // 比对对象
        Row objectRow = sheet.createRow(rowIndex++);
        Cell objectCell = objectRow.createCell(0);
        String comparisonObject = String.format("比对对象：%s vs %s",
                StringUtils.defaultIfBlank(request.getBaseServerIp(), request.getBaselineServer()),
                StringUtils.defaultIfBlank(request.getTargetServerIp(), request.getTargetServer()));
        objectCell.setCellValue(comparisonObject);
        objectCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(objectRow.getRowNum(), objectRow.getRowNum(), 0, 7));

        // 所属系统
        Row systemRow = sheet.createRow(rowIndex++);
        Cell systemCell = systemRow.createCell(0);
        String systemInfo = String.format("所属系统：%s",
                StringUtils.defaultIfBlank(request.getBusinessSystemName(), "未指定系统"));
        systemCell.setCellValue(systemInfo);
        systemCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(systemRow.getRowNum(), systemRow.getRowNum(), 0, 7));

        // 所属中心
        Row centerRow = sheet.createRow(rowIndex++);
        Cell centerCell = centerRow.createCell(0);
        String centerInfo = String.format("所属中心：%s vs %s",
                StringUtils.defaultIfBlank(request.getSourceCenterName(), "基线中心"),
                StringUtils.defaultIfBlank(request.getTargetCenterName(), "目标中心"));
        centerCell.setCellValue(centerInfo);
        centerCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(centerRow.getRowNum(), centerRow.getRowNum(), 0, 7));

        // 导出时间
        Row timeRow = sheet.createRow(rowIndex++);
        Cell timeCell = timeRow.createCell(0);
        String exportTime = "导出时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        timeCell.setCellValue(exportTime);
        timeCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(timeRow.getRowNum(), timeRow.getRowNum(), 0, 7));

        // 空行
        rowIndex++;

        return rowIndex;
    }

    /**
     * 创建说明区域
     *
     * @param sheet Excel工作表
     * @param styles 样式映射
     * @param startRow 起始行
     * @return 下一行索引
     */
    private int createDescriptionSection(Sheet sheet, Map<String, CellStyle> styles, int startRow) {
        int rowIndex = startRow;

        // 比对规则说明
        Row ruleRow = sheet.createRow(rowIndex++);
        Cell ruleCell = ruleRow.createCell(0);
        ruleCell.setCellValue("比对规则：基于基线服务采集内容与目标采集内容以行的形式进行行内容比对");
        ruleCell.setCellStyle(styles.get("description"));
        sheet.addMergedRegion(new CellRangeAddress(ruleRow.getRowNum(), ruleRow.getRowNum(), 0, 7));

        // 汇总行数说明
        Row summaryRow = sheet.createRow(rowIndex++);
        Cell summaryCell = summaryRow.createCell(0);
        summaryCell.setCellValue("基线汇总行数：基线汇总行数 = 最大行数 - 目标采集多出数 + 目标缺失行数");
        summaryCell.setCellStyle(styles.get("description"));
        sheet.addMergedRegion(new CellRangeAddress(summaryRow.getRowNum(), summaryRow.getRowNum(), 0, 7));

        Row summaryTargetRow = sheet.createRow(rowIndex++);
        Cell summaryTargetCell = summaryTargetRow.createCell(0);
        summaryTargetCell.setCellValue("目标汇总行数：目标汇总行数 = 最大行数 - 目标缺失行数");
        summaryTargetCell.setCellStyle(styles.get("description"));
        sheet.addMergedRegion(new CellRangeAddress(summaryTargetRow.getRowNum(), summaryTargetRow.getRowNum(), 0, 7));

        // 缺失行数说明
        Row missingRow = sheet.createRow(rowIndex++);
        Cell missingCell = missingRow.createCell(0);
        missingCell.setCellValue("缺失行数：目标采集内容行相比基线采集内容行缺失的行记录数，也就是基线存在的行，目标不存在的行的总数");
        missingCell.setCellStyle(styles.get("description"));
        sheet.addMergedRegion(new CellRangeAddress(missingRow.getRowNum(), missingRow.getRowNum(), 0, 7));

        // 多出行数说明
        Row extraRow = sheet.createRow(rowIndex++);
        Cell extraCell = extraRow.createCell(0);
        extraCell.setCellValue("多出行数：目标采集内容行相比基线采集内容行多出的行记录数，也就是基线不存在的行，目标存在的行的总数");
        extraCell.setCellStyle(styles.get("description"));
        sheet.addMergedRegion(new CellRangeAddress(extraRow.getRowNum(), extraRow.getRowNum(), 0, 7));

        // 基线采集内容说明
        Row baselineRow = sheet.createRow(rowIndex++);
        Cell baselineCell = baselineRow.createCell(0);
        baselineCell.setCellValue("基线采集内容：基线采集的实际内容");
        baselineCell.setCellStyle(styles.get("description"));
        sheet.addMergedRegion(new CellRangeAddress(baselineRow.getRowNum(), baselineRow.getRowNum(), 0, 7));

        // 目标采集内容说明
        Row targetRow = sheet.createRow(rowIndex++);
        Cell targetCell = targetRow.createCell(0);
        targetCell.setCellValue("目标采集内容：目标采集的实际内容");
        targetCell.setCellStyle(styles.get("description"));
        sheet.addMergedRegion(new CellRangeAddress(targetRow.getRowNum(), targetRow.getRowNum(), 0, 7));

        // 空行
        rowIndex++;

        return rowIndex;
    }

    /**
     * 设置单元格边框
     *
     * @param style 单元格样式
     */
    private void setBorders(CellStyle style) {
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * 创建汇总统计表格
     *
     * @param sheet Excel工作表
     * @param result 比对结果
     * @param request 请求参数
     * @param styles 样式映射
     * @param startRow 起始行
     * @return 下一行索引
     */
    private int createSummaryTable(Sheet sheet, HtmlComparisonResultDto result, HtmlComparisonRequestDto request,
                                 Map<String, CellStyle> styles, int startRow) {
        int rowIndex = startRow;

        // 表头行
        Row headerRow = sheet.createRow(rowIndex++);
        String[] headers = {"服务器类型", "IP", "hostname", "汇总", "缺失", "多出", "不一致", "一致"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.get("header"));
        }

        // 基线服务器行
        Row baselineRow = sheet.createRow(rowIndex++);
        baselineRow.createCell(0).setCellValue("基线服务器");
        baselineRow.createCell(1).setCellValue(StringUtils.defaultIfBlank(request.getBaseServerIp(), ""));
        baselineRow.createCell(2).setCellValue(StringUtils.defaultIfBlank(request.getBaselineServer(), ""));
        baselineRow.createCell(3).setCellValue(result.getTotalSourceFiles());
        baselineRow.createCell(4).setCellValue(""); // 基线服务器缺失列为空
        baselineRow.createCell(5).setCellValue(""); // 基线服务器多出列为空
        baselineRow.createCell(6).setCellValue(""); // 基线服务器不一致列为空
        baselineRow.createCell(7).setCellValue(""); // 基线服务器一致列为空

        for (int i = 0; i < 8; i++) {
            baselineRow.getCell(i).setCellStyle(styles.get("data"));
        }

        // 目标服务器行
        Row targetRow = sheet.createRow(rowIndex++);
        targetRow.createCell(0).setCellValue("目标服务器");
        targetRow.createCell(1).setCellValue(StringUtils.defaultIfBlank(request.getTargetServerIp(), ""));
        targetRow.createCell(2).setCellValue(StringUtils.defaultIfBlank(request.getTargetServer(), ""));
        targetRow.createCell(3).setCellValue(result.getTotalTargetFiles());
        targetRow.createCell(4).setCellValue(result.getMissingCount()); // 目标相对于基线的缺失
        targetRow.createCell(5).setCellValue(result.getExtraCount()); // 目标相对于基线的多出
        targetRow.createCell(6).setCellValue(result.getInconsistentCount());
        targetRow.createCell(7).setCellValue(result.getConsistentCount());

        for (int i = 0; i < 8; i++) {
            targetRow.getCell(i).setCellStyle(styles.get("data"));
        }

        // 空行
        rowIndex++;

        return rowIndex;
    }

    /**
     * 创建文本内容比对详情
     *
     * @param sheet Excel工作表
     * @param result 比对结果
     * @param styles 样式映射
     * @param startRow 起始行
     * @return 下一行索引
     */
    private int createDetailFileList(Sheet sheet, HtmlComparisonResultDto result,
                                   Map<String, CellStyle> styles, int startRow) {
        int rowIndex = startRow;

        // 文本内容比对详情标题 - 扩展合并区域使表格更宽
        Row listTitleRow = sheet.createRow(rowIndex++);
        Cell listTitleCell = listTitleRow.createCell(0);
        listTitleCell.setCellValue("文本内容比对详情");
        listTitleCell.setCellStyle(styles.get("title"));
        // 扩展合并区域，使详情表格比汇总表格更宽
        sheet.addMergedRegion(new CellRangeAddress(listTitleRow.getRowNum(), listTitleRow.getRowNum(), 0, 7));

        // 文本比对表头 - 扩展表头以支持更宽的内容显示
        Row contentHeaderRow = sheet.createRow(rowIndex++);
        contentHeaderRow.createCell(0).setCellValue("类型");
        contentHeaderRow.createCell(1).setCellValue("行号");

        // 基线采集内容列 - 合并多列以增加显示宽度
        contentHeaderRow.createCell(2).setCellValue("基线采集内容");
        sheet.addMergedRegion(new CellRangeAddress(contentHeaderRow.getRowNum(), contentHeaderRow.getRowNum(), 2, 4));

        // 目标采集内容列 - 合并3列，与基线采集内容宽度保持一致
        contentHeaderRow.createCell(5).setCellValue("目标采集内容");
        sheet.addMergedRegion(new CellRangeAddress(contentHeaderRow.getRowNum(), contentHeaderRow.getRowNum(), 5, 7));

        // 设置表头样式 - 调整到8列
        for (int i = 0; i < 8; i++) {
            if (contentHeaderRow.getCell(i) != null) {
                contentHeaderRow.getCell(i).setCellStyle(styles.get("header"));
            } else {
                // 为合并的单元格创建空单元格并设置样式
                Cell cell = contentHeaderRow.createCell(i);
                cell.setCellStyle(styles.get("header"));
            }
        }

        // 收集所有比对行并按行号排序
        List<TextLineComparisonDto> allComparisons = collectAndSortTextComparisons(result);

        // 填充比对数据
        for (TextLineComparisonDto comparison : allComparisons) {
            Row contentRow = sheet.createRow(rowIndex++);

            // 类型
            contentRow.createCell(0).setCellValue(comparison.getStatus());

            // 行号（使用解析出的行号）
            contentRow.createCell(1).setCellValue(comparison.getLineNumber());

            // 基线采集内容（HTML解码）- 合并列2-4以增加显示宽度
            String baselineContent = StringEscapeUtils.unescapeHtml4(
                StringUtils.defaultIfBlank(comparison.getBaselineContent(), ""));
            Cell baselineCell = contentRow.createCell(2);
            baselineCell.setCellValue(limitContentLength(baselineContent));
            sheet.addMergedRegion(new CellRangeAddress(contentRow.getRowNum(), contentRow.getRowNum(), 2, 4));

            // 目标采集内容（HTML解码）- 合并列5-7，与基线采集内容宽度一致
            String targetContent = StringEscapeUtils.unescapeHtml4(
                StringUtils.defaultIfBlank(comparison.getTargetContent(), ""));
            Cell targetCell = contentRow.createCell(5);
            targetCell.setCellValue(limitContentLength(targetContent));
            sheet.addMergedRegion(new CellRangeAddress(contentRow.getRowNum(), contentRow.getRowNum(), 5, 7));

            // 根据状态设置样式 - 调整到8列，统一设置自动换行
            CellStyle rowStyle = getStyleByStatus(styles, comparison.getStatus());

            for (int i = 0; i < 8; i++) {
                if (contentRow.getCell(i) != null) {
                    contentRow.getCell(i).setCellStyle(rowStyle);
                } else {
                    // 为合并的单元格创建空单元格并设置样式
                    Cell cell = contentRow.createCell(i);
                    cell.setCellStyle(rowStyle);
                }
            }
        }

        return rowIndex;
    }

    /**
     * 收集并排序文本比对结果
     *
     * @param result HTML比对结果
     * @return 排序后的文本行比对列表
     */
    private List<TextLineComparisonDto> collectAndSortTextComparisons(HtmlComparisonResultDto result) {
        List<TextLineComparisonDto> allComparisons = new ArrayList<>();

        // 转换不一致文件为文本行比对
        if (result.getInconsistentFiles() != null) {
            for (FileInfoDto item : result.getInconsistentFiles()) {
                TextLineComparisonDto comparison = convertToTextLineComparison(item, "不一致");
                if (comparison != null) {
                    allComparisons.add(comparison);
                }
            }
        }

        // 转换缺失文件为文本行比对
        if (result.getMissingFiles() != null) {
            for (FileInfoDto item : result.getMissingFiles()) {
                TextLineComparisonDto comparison = convertToTextLineComparison(item, "缺失");
                if (comparison != null) {
                    comparison.setTargetContent(""); // 缺失内容显示为空
                    allComparisons.add(comparison);
                }
            }
        }

        // 转换多出文件为文本行比对
        if (result.getExtraFiles() != null) {
            for (FileInfoDto item : result.getExtraFiles()) {
                TextLineComparisonDto comparison = convertToTextLineComparison(item, "多出");
                if (comparison != null) {
                    comparison.setBaselineContent(""); // 不存在内容显示为空
                    // 多出的内容应该在目标内容中显示
                    String content = extractContentFromPath(item.getFilePath());
                    comparison.setTargetContent(content);
                    allComparisons.add(comparison);
                }
            }
        }

        // 转换一致文件为文本行比对（限制数量）
        if (result.getConsistentFiles() != null) {
            int maxShow = Math.min(20, result.getConsistentFiles().size());
            for (int i = 0; i < maxShow; i++) {
                FileInfoDto item = result.getConsistentFiles().get(i);
                TextLineComparisonDto comparison = convertToTextLineComparison(item, "一致");
                if (comparison != null) {
                    // 一致内容，基线和目标内容相同
                    comparison.setTargetContent(comparison.getBaselineContent());
                    allComparisons.add(comparison);
                }
            }
        }

        // 只按行号排序，更好地体现文本内容在原始位置上的差异情况
        allComparisons.sort((a, b) -> {
            return Integer.compare(
                a.getLineNumber() != null ? a.getLineNumber() : 0,
                b.getLineNumber() != null ? b.getLineNumber() : 0
            );
        });

        return allComparisons;
    }

    /**
     * 将FileInfoDto转换为TextLineComparisonDto
     *
     * @param item 文件信息项
     * @param status 状态
     * @return 文本行比对DTO
     */
    private TextLineComparisonDto convertToTextLineComparison(FileInfoDto item, String status) {
        if (item == null) {
            return null;
        }

        TextLineComparisonDto comparison = new TextLineComparisonDto();
        comparison.setStatus(status);

        // 从filePath中解析行号和内容
        // 假设filePath格式为：行号:内容 或者直接是内容
        String filePath = item.getFilePath();
        String content = "";
        if (StringUtils.isNotBlank(filePath)) {
            // 尝试解析行号
            Integer lineNumber = parseLineNumber(filePath);
            comparison.setLineNumber(lineNumber);

            // 提取内容（去除行号部分）
            content = extractContentFromPath(filePath);
            comparison.setBaselineContent(content);
        }

        // 设置目标内容
        if ("不一致".equals(status)) {
            // 从remark中提取目标内容
            String remark = item.getRemark();
            if (StringUtils.isNotBlank(remark) && remark.startsWith("目标内容:")) {
                String targetContent = remark.substring("目标内容:".length());
                // 如果目标内容也包含行号前缀，需要提取实际内容
                String actualTargetContent = extractContentFromPath(targetContent);
                comparison.setTargetContent(actualTargetContent);
            } else {
                // 如果没有找到目标内容，使用基线内容作为默认值
                comparison.setTargetContent(content);
            }
        }

        return comparison;
    }

    /**
     * 从文件路径中解析行号
     *
     * @param filePath 文件路径
     * @return 行号
     */
    private Integer parseLineNumber(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return 0;
        }

        // 尝试从路径开头解析数字行号
        String[] parts = filePath.split(":", 2);
        if (parts.length >= 1) {
            try {
                return Integer.parseInt(parts[0].trim());
            } catch (NumberFormatException e) {
                // 如果解析失败，尝试从其他位置查找数字
                String numberStr = filePath.replaceAll("[^0-9]", "");
                if (StringUtils.isNotBlank(numberStr)) {
                    try {
                        return Integer.parseInt(numberStr.substring(0, Math.min(numberStr.length(), 6)));
                    } catch (NumberFormatException ex) {
                        // 忽略
                    }
                }
            }
        }

        return 0; // 默认行号
    }

    /**
     * 从文件路径中提取内容
     *
     * @param filePath 文件路径
     * @return 内容
     */
    private String extractContentFromPath(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return "";
        }

        // 如果包含冒号，取冒号后的内容
        String[] parts = filePath.split(":", 2);
        if (parts.length >= 2) {
            return parts[1].trim();
        }

        // 否则返回整个路径
        return filePath;
    }



    /**
     * 根据状态获取样式
     *
     * @param styles 样式映射
     * @param status 状态
     * @return 单元格样式
     */
    private CellStyle getStyleByStatus(Map<String, CellStyle> styles, String status) {
        if (status == null) {
            return styles.get("data");
        }

        switch (status) {
            case "不一致":
                return styles.get("inconsistent");
            case "缺失":
                return styles.get("missing");
            case "多出":
                return styles.get("extra");
            case "一致":
                return styles.get("consistent");
            default:
                return styles.get("data");
        }
    }

    /**
     * 限制内容长度
     *
     * @param content 内容
     * @return 限制长度后的内容
     */
    private String limitContentLength(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }

        /* 限制内容长度，避免单元格过宽
        if (content.length() > 150) {
            return content.substring(0, 150) + "...";
        }
        */
        return content;
    }

    /**
     * 设置列宽
     *
     * @param sheet Excel工作表
     */
    private void setColumnWidths(Sheet sheet) {
        // 设置列宽，基线采集内容和目标采集内容宽度保持一致
        // A列：类型
        sheet.setColumnWidth(0, 15 * 256);
        // B列：行号
        sheet.setColumnWidth(1, 15 * 256);
        // C列：基线采集内容（合并列2-4）
        sheet.setColumnWidth(2, 30 * 256);
        // D列：基线采集内容（合并列2-4）
        sheet.setColumnWidth(3, 30 * 256);
        // E列：基线采集内容（合并列2-4）
        sheet.setColumnWidth(4, 30 * 256);
        // F列：目标采集内容（合并列5-7）
        sheet.setColumnWidth(5, 30 * 256);
        // G列：目标采集内容（合并列5-7）
        sheet.setColumnWidth(6, 30 * 256);
        // H列：目标采集内容（合并列5-7）
        sheet.setColumnWidth(7, 30 * 256);
    }

    /**
     * 格式化文件基本信息
     *
     * @param file 文件信息
     * @return 格式化字符串
     */
    private String formatFileBasicInfo(FileInfoDto file) {
        if (file == null) {
            return "";
        }

        StringBuilder info = new StringBuilder();
        if (StringUtils.isNotBlank(file.getFileSize())) {
            info.append("大小: ").append(file.getFileSize());
        }
        if (StringUtils.isNotBlank(file.getPermissions())) {
            if (info.length() > 0) {
                info.append(", ");
            }
            info.append("权限: ").append(file.getPermissions());
        }
        if (StringUtils.isNotBlank(file.getMd5())) {
            if (info.length() > 0) {
                info.append(", ");
            }
            info.append("MD5: ").append(file.getMd5().substring(0, Math.min(8, file.getMd5().length()))).append("...");
        }

        return info.toString();
    }

    /**
     * 格式化文件信息为字符串
     *
     * @param file 文件信息
     * @return 格式化字符串
     */
    private String formatFileInfo(FileInfoDto file) {
        if (file == null) {
            return "";
        }

        return String.format("%s (size: %s, permissions: %s, MD5: %s)",
                file.getFilePath(),
                StringUtils.defaultIfBlank(file.getFileSize(), "unknown"),
                StringUtils.defaultIfBlank(file.getPermissions(), "unknown"),
                StringUtils.defaultIfBlank(file.getMd5(), "unknown"));
    }
}
