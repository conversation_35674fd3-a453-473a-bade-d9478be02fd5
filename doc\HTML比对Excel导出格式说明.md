# HTML比对Excel导出格式说明

## 概述

HTML比对功能的Excel导出专门用于**文本内容比对**，严格按照提供的格式要求设计，采用专门的POI实现。该功能对字符串内容进行逐行比对分析，识别文本行的一致、不一致、缺失、多出状态，与原有的目录文件比对导出格式完全不同。

## Excel格式结构

### 整体布局
- **单Sheet设计**：环境一致性比对结果
- **四个主要区域**：标题区域、说明区域、汇总统计表格、详细文件列表
- **专业样式**：不同颜色背景、边框、字体样式

## 详细格式说明

### 1. 标题区域（黑色粗体）

#### 位置：A1-H3行
- **A1行**：环境一致性比对结果报告（合并A1:H1）
- **A2行**：比对对象：************* vs *************（合并A2:H2）
- **A3行**：比对时间：2025-01-29 15:30:45（合并A3:H3）

#### 样式特点：
- 字体：宋体，12pt，黑色粗体
- 对齐：左对齐，垂直居中
- 背景：无背景色
- 边框：无边框

### 2. 说明区域（红色粗体）

#### 位置：A5-H7行
- **A5行**：比对规则：基于文件路径、大小、权限、MD5值进行全面比对分析
- **A6行**：计算公式：一致率=一致文件数/基线文件总数×100%，不一致率=不一致文件数/基线文件总数×100%
- **A7行**：路径说明：显示完整的文件路径，便于定位和处理差异文件

#### 样式特点：
- 字体：宋体，10pt，红色粗体
- 对齐：左对齐，垂直居中
- 背景：无背景色
- 边框：无边框

### 3. 汇总统计表格

#### 位置：A9-H11行

#### 表头行（A9行）：
| 列 | 内容 | 样式 |
|----|------|------|
| A9 | 服务器类型 | 蓝色背景，白色粗体，居中 |
| B9 | IP | 蓝色背景，白色粗体，居中 |
| C9 | hostname | 蓝色背景，白色粗体，居中 |
| D9 | 汇总 | 蓝色背景，白色粗体，居中 |
| E9 | 缺失 | 蓝色背景，白色粗体，居中 |
| F9 | 多出 | 蓝色背景，白色粗体，居中 |
| G9 | 不一致 | 蓝色背景，白色粗体，居中 |
| H9 | 一致 | 蓝色背景，白色粗体，居中 |

#### 基线服务器行（A10行）：
| 列 | 内容示例 | 说明 |
|----|----------|------|
| A10 | 基线服务器 | 服务器类型 |
| B10 | ************* | 基线服务器IP |
| C10 | prod-server-01 | 基线服务器主机名 |
| D10 | 100 | 基线文件总数 |
| E10 | 0 | 基线不存在缺失 |
| F10 | 10 | 相对于目标的多出 |
| G10 | 5 | 不一致文件数 |
| H10 | 85 | 一致文件数 |

#### 目标服务器行（A11行）：
| 列 | 内容示例 | 说明 |
|----|----------|------|
| A11 | 目标服务器 | 服务器类型 |
| B11 | ************* | 目标服务器IP |
| C11 | test-server-01 | 目标服务器主机名 |
| D11 | 95 | 目标文件总数 |
| E11 | 10 | 相对于基线的缺失 |
| F11 | 5 | 相对于基线的多出 |
| G11 | 5 | 不一致文件数 |
| H11 | 85 | 一致文件数 |

#### 样式特点：
- 表头：蓝色背景，白色粗体，居中对齐，完整边框
- 数据：白色背景，黑色字体，居中对齐，完整边框

### 4. 详细文件列表

#### 位置：A13行开始

#### 列表标题（A13行）：
- 内容：详细文件列表
- 样式：黑色粗体，合并A13:H13

#### 文本比对表头（A14行）：
| 列 | 内容 | 宽度 |
|----|------|------|
| A14 | 类型 | 12字符 |
| B14 | 行号 | 8字符 |
| C14 | 基线采集内容 | 60字符 |
| D14 | 目标采集内容 | 60字符 |

#### 文本行数据样式：

##### 不一致文本行（红色背景）：
- 类型：不一致
- 行号：从HTML解析出的行号
- 基线采集内容：HTML解码后的基线文本内容
- 目标采集内容：HTML解码后的目标文本内容

##### 缺失文本行（浅蓝色背景）：
- 类型：缺失
- 行号：从HTML解析出的行号
- 基线采集内容：HTML解码后的基线文本内容
- 目标采集内容：空（显示为空白）

##### 多出文本行（浅黄色背景）：
- 类型：多出
- 行号：从HTML解析出的行号
- 基线采集内容：空（显示为空白）
- 目标采集内容：HTML解码后的目标文本内容

##### 一致文本行（绿色背景）：
- 类型：一致
- 行号：从HTML解析出的行号
- 基线采集内容：HTML解码后的文本内容
- 目标采集内容：HTML解码后的文本内容（相同）

## 技术实现特点

### 1. 文本内容比对专用实现
```java
// 使用XSSFWorkbook创建Excel
Workbook workbook = new XSSFWorkbook();

// 创建专门的样式映射
Map<String, CellStyle> styles = createHtmlComparisonStyles(workbook);

// 分区域创建内容
createTitleSection(sheet, result, request, styles, rowIndex);
createDescriptionSection(sheet, styles, rowIndex);
createSummaryTable(sheet, result, request, styles, rowIndex);
createDetailFileList(sheet, result, styles, rowIndex);
```

### 2. HTML行号解析和排序
```java
// 从HTML字符串中解析行号
private Integer parseLineNumber(String filePath) {
    String[] parts = filePath.split(":", 2);
    if (parts.length >= 1) {
        try {
            return Integer.parseInt(parts[0].trim());
        } catch (NumberFormatException e) {
            // 处理解析失败情况
        }
    }
    return 0;
}

// 按类型和行号排序
allComparisons.sort((a, b) -> {
    int statusOrder1 = getStatusOrder(a.getStatus());
    int statusOrder2 = getStatusOrder(b.getStatus());

    if (statusOrder1 != statusOrder2) {
        return Integer.compare(statusOrder1, statusOrder2);
    }

    // 同状态按行号排序
    return Integer.compare(a.getLineNumber(), b.getLineNumber());
});
```

### 3. HTML内容解码处理
```java
// 使用StringEscapeUtils进行HTML解码
String baselineContent = StringEscapeUtils.unescapeHtml4(
    StringUtils.defaultIfBlank(comparison.getBaselineContent(), ""));
String targetContent = StringEscapeUtils.unescapeHtml4(
    StringUtils.defaultIfBlank(comparison.getTargetContent(), ""));
```

### 2. 样式系统
- **标题样式**：黑色粗体，12pt，左对齐
- **说明样式**：红色粗体，10pt，左对齐
- **表头样式**：蓝色背景，白色粗体，居中，完整边框
- **数据样式**：白色背景，黑色字体，居中，完整边框
- **文件状态样式**：不同背景色区分文件状态

### 3. 自动化功能
- **列宽自动调整**：根据内容类型设置合适宽度
- **单元格合并**：标题和说明区域自动合并
- **边框处理**：确保所有表格区域有完整边框
- **字体统一**：全部使用宋体，确保显示一致性

### 4. 数据优化
- **一致文件限制**：只显示前10个一致文件，避免文件过大
- **信息精简**：MD5值只显示前8位，节省空间
- **智能分组**：按文件状态分组显示，便于查看

## 与原有导出格式的区别

### 原有格式特点：
- 双Sheet结构（比对结果 + 一致文件列表）
- 简单的表格形式
- 基础的颜色区分
- 标准的列头结构

### HTML比对格式特点：
- 单Sheet复合结构
- 四个功能区域
- 丰富的样式系统
- 专业的报告格式
- 详细的说明信息
- 智能的数据展示

## 使用示例

### 1. 服务层调用
```java
@Autowired
private IHtmlComparisonService htmlComparisonService;

HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
request.setFlowId(12345L);

HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);
htmlComparisonService.exportHtmlComparisonResult(request, result, response);
```

### 2. 组件层调用
```java
@Autowired
private HtmlComparisonComponent htmlComparisonComponent;

htmlComparisonComponent.parseAndExport(flowId, "基线服务器", "目标服务器", response);
```

### 3. REST API调用
```http
POST /htmlComparison/export
Content-Type: application/x-www-form-urlencoded

flowId=12345
```

## 文件命名规则

- **格式**：环境一致性比对结果_yyyyMMdd_HHmmss.xlsx
- **示例**：环境一致性比对结果_20250129_153045.xlsx
- **编码**：UTF-8编码，支持中文文件名

## 性能特点

- **内存优化**：使用流式写入，支持大数据量导出
- **速度优化**：专门的样式缓存，避免重复创建
- **文件大小**：智能数据筛选，控制文件大小
- **兼容性**：标准XLSX格式，兼容所有Excel版本

## 总结

HTML比对Excel导出功能完全按照提供的格式要求实现，具有专业的报告样式、丰富的数据展示和良好的用户体验。与原有的目录文件比对导出格式完全独立，满足不同业务场景的需求。
