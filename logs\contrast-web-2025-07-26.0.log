2025-07-26 00:07:42.597 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 00:17:42.130 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 01:07:42.177 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 01:07:42.746 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 01:57:42.242 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 02:07:42.807 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 02:47:42.286 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 03:07:42.871 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 03:37:42.339 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 04:07:42.930 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 04:27:42.389 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 05:07:42.984 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 05:17:42.444 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 05:44:41.041 [DubboMetadataReportTimer-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:434 -  [DUBBO] start to publish all metadata., dubbo version: 3.2.5, current host: **********
2025-07-26 05:44:41.051 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@132e736d; definition: {group=system, release=3.2.5, side=consumer, interface=com.ideal.system.api.ICenter, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753408657372}, dubbo version: 3.2.5, current host: **********
2025-07-26 05:44:41.061 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@560298a6; definition: {group=engine, release=3.2.5, side=consumer, interface=com.ideal.engine.api.IStartFlow, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=killFlow,pauseFlow,resumeFlow,startFlow, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1753408662241}, dubbo version: 3.2.5, current host: **********
2025-07-26 05:44:41.069 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@5e474cee; definition: {group=engine, release=3.2.5, side=consumer, interface=com.ideal.engine.api.IActivity, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1753408662798}, dubbo version: 3.2.5, current host: **********
2025-07-26 05:44:41.076 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@6fb6039a; definition: {group=system, release=3.2.5, side=consumer, interface=com.ideal.system.api.IRoleProjectPermission, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getRoleButtonAuthority, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753408661802}, dubbo version: 3.2.5, current host: **********
2025-07-26 05:44:41.080 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@7c686e64; definition: {group=system, release=3.2.5, side=consumer, interface=com.ideal.system.api.IBusinessSystem, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753408663213}, dubbo version: 3.2.5, current host: **********
2025-07-26 05:44:41.081 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@231d18a0; definition: {group=system, release=3.2.5, side=consumer, interface=com.ideal.system.api.IBusinessSystemCompuerList, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753408663672}, dubbo version: 3.2.5, current host: **********
2025-07-26 06:07:42.495 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 06:07:43.050 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 06:57:42.546 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 07:07:43.104 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 07:47:42.582 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 08:07:43.139 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 08:37:42.627 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 09:07:43.199 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 09:27:43.111 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:02.981 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xc0c88572, L:/**********:62524 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.059 [NettyClientWorker-9-2] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:62524 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.317 [nacos-grpc-client-executor-************-32276] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:42:03.330 [nacos-grpc-client-executor-************-32276] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753343330105"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:42:03.331 [nacos-grpc-client-executor-************-32276] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-26 09:42:03.339 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@487e8ba7
2025-07-26 09:42:03.371 [nacos-grpc-client-executor-************-32276] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:42:03.374 [nacos-grpc-client-executor-************-32277] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:42:03.378 [nacos-grpc-client-executor-************-32277] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753343331504"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:42:03.378 [nacos-grpc-client-executor-************-32277] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-26 09:42:03.379 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@57489cc1
2025-07-26 09:42:03.382 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-26 09:42:03.382 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-26 09:42:03.384 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=31248&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753408662241&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.385 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=31248&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753408662798&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.398 [nacos-grpc-client-executor-************-32277] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:42:03.417 [nacos-grpc-client-executor-************-32211] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:42:03.419 [nacos-grpc-client-executor-************-32211] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1753343331504"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:42:03.419 [nacos-grpc-client-executor-************-32211] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-26 09:42:03.421 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@47c237fe
2025-07-26 09:42:03.448 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.449 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.452 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.453 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-26 09:42:03.459 [nacos-grpc-client-executor-************-32211] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:42:03.465 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.465 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.483 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.486 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.487 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.487 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-26 09:42:03.495 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0xc0c88572, L:/**********:62524 ! R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.529 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-26 09:42:03.529 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.184 [nacos-grpc-client-executor-************-32288] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Receive server push request, request = NotifySubscriberRequest, requestId = 351737
2025-07-26 09:45:30.185 [nacos-grpc-client-executor-************-32354] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:45:30.184 [nacos-grpc-client-executor-************-32288] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1753494328160"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:45:30.185 [nacos-grpc-client-executor-************-32354] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753494326932"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:45:30.185 [nacos-grpc-client-executor-************-32354] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753494326932"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:45:30.185 [nacos-grpc-client-executor-************-32288] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1753494328160"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:45:30.185 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@c5e5ef71
2025-07-26 09:45:30.187 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=31248&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753408662241&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.195 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@47c237fe
2025-07-26 09:45:30.196 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.205 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.207 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.599 [nacos-grpc-client-executor-************-32288] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Ack server push request, request = NotifySubscriberRequest, requestId = 351737
2025-07-26 09:45:30.599 [nacos-grpc-client-executor-************-32354] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:45:30.767 [nacos-grpc-client-executor-************-32355] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:45:30.768 [nacos-grpc-client-executor-************-32355] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753494328160"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:45:30.768 [nacos-grpc-client-executor-************-32355] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753494328160"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-26 09:45:30.770 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0x5798c3fc, L:/**********:64185 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.771 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.770 [NettyClientWorker-9-3] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:64185 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.796 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.824 [nacos-grpc-client-executor-************-32355] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-26 09:45:30.830 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.831 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.832 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.832 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.833 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.855 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.861 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:60 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.862 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.862 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@615c3f76
2025-07-26 09:45:30.864 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=31248&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753408662798&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.879 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-26 09:45:30.884 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-26 10:07:43.257 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 10:17:43.267 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 11:07:43.332 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 11:07:43.341 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 11:57:43.393 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 12:07:43.396 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 12:47:43.441 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 13:07:43.462 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 13:37:43.493 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 14:07:43.531 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 14:27:43.544 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 15:07:43.592 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 15:17:43.596 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 16:07:43.649 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 16:07:43.653 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 16:57:43.678 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 17:07:43.715 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 17:47:43.732 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 18:07:43.792 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 18:37:43.785 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 19:07:43.936 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 19:27:43.836 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 20:07:44.028 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 20:17:43.891 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 21:07:43.943 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 21:07:44.098 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 21:57:43.998 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 22:07:44.171 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 22:47:44.047 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-26 23:07:44.244 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-26 23:37:44.093 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
