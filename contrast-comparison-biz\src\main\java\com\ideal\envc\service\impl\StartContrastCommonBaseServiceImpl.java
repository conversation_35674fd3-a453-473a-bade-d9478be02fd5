package com.ideal.envc.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.common.ContrastConstants;
import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.model.bean.HierarchicalRunInstanceBean;
import com.ideal.envc.model.bean.HierarchicalRunInstanceInfoBean;
import com.ideal.envc.model.bean.HierarchicalRunRuleBean;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartFlowDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;
import com.ideal.envc.model.enums.RuleITypeEnums;
import com.ideal.envc.model.enums.RuleMatchTypeEnums;
import com.ideal.envc.model.enums.RuleModelEnum;
import com.ideal.envc.model.enums.RuleWayEnums;
import com.ideal.envc.producer.SendDataToEngineMqProducer;
import com.ideal.envc.service.IStartContrastBaseService;
import com.ideal.envc.service.IStartContrastCommonBaseService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Service
public class StartContrastCommonBaseServiceImpl implements IStartContrastCommonBaseService {
    private final static Logger logger = LoggerFactory.getLogger(StartContrastCommonBaseServiceImpl.class);
    private final SendDataToEngineMqProducer sendDataToEngineMqProducer;
    private final IStartContrastBaseService startContrastBaseService;


    public StartContrastCommonBaseServiceImpl(SendDataToEngineMqProducer sendDataToEngineMqProducer, IStartContrastBaseService startContrastBaseService) {
        this.sendDataToEngineMqProducer = sendDataToEngineMqProducer;
        this.startContrastBaseService = startContrastBaseService;
    }

    /**
     * 基于层次化实例数据封装出任务流DTO列表
     *
     * @param hierarchicalInstanceList 层次化实例数据列表
     * @return 任务流DTO列表
     */
    @Override
    public List<StartTaskFlowDto> buildTaskFlowDtoList(List<HierarchicalRunInstanceBean> hierarchicalInstanceList, UserDto userDto) {
        if (CollectionUtils.isEmpty(hierarchicalInstanceList)) {
            logger.warn("层次化实例数据列表为空，无法构建任务流DTO列表");
            return new ArrayList<>();
        }

        logger.info("开始构建任务流DTO列表，实例数量：{}", hierarchicalInstanceList.size());
        List<StartTaskFlowDto> taskFlowDtoList = new ArrayList<>();

        // 遍历实例列表，每个实例生成一个StartTaskFlowDto
        for (HierarchicalRunInstanceBean instanceBean : hierarchicalInstanceList) {
            StartTaskFlowDto taskFlowDto = processInstanceBean(instanceBean, userDto);
            if (taskFlowDto != null) {
                taskFlowDtoList.add(taskFlowDto);
            }
        }

        logger.info("构建任务流DTO列表完成，任务流数量：{}", taskFlowDtoList.size());
        return taskFlowDtoList;
    }

    /**
     * 处理单个实例Bean，生成StartTaskFlowDto
     *
     * @param instanceBean 层次化实例Bean
     * @param userDto 用户信息
     * @return StartTaskFlowDto或null（如果没有有效任务）
     */
    private StartTaskFlowDto processInstanceBean(HierarchicalRunInstanceBean instanceBean, UserDto userDto) {
        // 如果实例没有实例详情，则跳过
        if (CollectionUtils.isEmpty(instanceBean.getInstanceInfoList())) {
            logger.warn("实例{}没有实例详情，跳过构建任务流DTO", instanceBean.getId());
            return null;
        }

        // 为每个实例创建一个StartTaskFlowDto
        StartTaskFlowDto taskFlowDto = new StartTaskFlowDto();
        taskFlowDto.setUniqueTaskId(instanceBean.getId());

        // 创建任务列表
        List<StartFlowDto> tasks = buildTasksForInstance(instanceBean, userDto);

        // 如果没有收集到任务，则跳过该实例
        if (tasks.isEmpty()) {
            logger.warn("实例{}没有收集到有效的任务，跳过", instanceBean.getId());
            return null;
        }

        taskFlowDto.setTasks(tasks);
        return taskFlowDto;
    }

    /**
     * 为实例构建任务列表
     *
     * @param instanceBean 层次化实例Bean
     * @param userDto 用户信息
     * @return 任务列表
     */
    private List<StartFlowDto> buildTasksForInstance(HierarchicalRunInstanceBean instanceBean, UserDto userDto) {
        List<StartFlowDto> tasks = new ArrayList<>();

        // 收集所有规则，每个规则生成一个StartFlowDto
        for (HierarchicalRunInstanceInfoBean infoBean : instanceBean.getInstanceInfoList()) {
            // 如果实例详情没有规则，则跳过
            if (CollectionUtils.isEmpty(infoBean.getRuleList())) {
                logger.warn("实例详情{}没有规则，跳过", infoBean.getId());
                continue;
            }

            // 遍历规则列表，每个规则生成一个StartFlowDto
            for (HierarchicalRunRuleBean ruleBean : infoBean.getRuleList()) {
                StartFlowDto task = createStartFlowDto(instanceBean, infoBean, ruleBean, userDto);
                tasks.add(task);
            }
        }

        return tasks;
    }

    /**
     * 创建StartFlowDto任务对象
     *
     * @param instanceBean 实例Bean
     * @param infoBean 实例详情Bean
     * @param ruleBean 规则Bean
     * @param userDto 用户信息
     * @return StartFlowDto任务对象
     */
    private StartFlowDto createStartFlowDto(HierarchicalRunInstanceBean instanceBean, 
                                          HierarchicalRunInstanceInfoBean infoBean, 
                                          HierarchicalRunRuleBean ruleBean, 
                                          UserDto userDto) {
        StartFlowDto task = new StartFlowDto();
        
        // 设置基本属性
        task.setTaskFlowId(ruleBean.getRuleFlow().getId());
        task.setPrjName(ContrastConstants.CONTRAST_PRJ_NAME);
        task.setFlowName(getFlowNameByRuleType(ruleBean.getType()));
        task.setIinsName("Instance_" + instanceBean.getId() + "_" + ruleBean.getId());
        task.setComment("一致性比对任务：" + instanceBean.getId() + "-类型：" + RuleITypeEnums.getByCode(ruleBean.getType()));
        task.setStartTime(null);
        task.setType(ContrastConstants.CONTRAST_PRJ_TYPE);
        task.setPrjType(ContrastConstants.CONTRAST_PRJ_TYPE);
        task.setIsAdvance(false);
        task.setUserInfo(userDto);
        task.setLogConfig(null);
        task.setForceEfficiencyPrior(false);
        task.setStarterSucElem(null);

        // 设置参数列表
        List<Serializable> args = buildTaskArgs(infoBean, ruleBean);
        task.setArgs(args);

        // 设置环境变量
        Map<String, Serializable> envVars = new HashMap<>();
        envVars.put(ContrastConstants.CONTRAST_FLOW_ENV_BUTTERFLY_VERSION, null);
        task.setEnvVars(envVars);
        
        return task;
    }

    /**
     * 根据规则类型获取流程名称
     *
     * @param ruleType 规则类型
     * @return 流程名称
     */
    private String getFlowNameByRuleType(Long ruleType) {
        if (RuleITypeEnums.DIRECTORY.getCode().equals(ruleType)) {
            return ContrastConstants.CONTRAST_DIR_COMPARE_FLOW_NAME;
        } else {
            return ContrastConstants.CONTRAST_FILE_COMPARE_FLOW_NAME;
        }
    }

    /**
     * 构建任务参数列表
     *
     * @param infoBean 实例详情Bean
     * @param ruleBean 规则Bean
     * @return 参数列表
     */
    private List<Serializable> buildTaskArgs(HierarchicalRunInstanceInfoBean infoBean, HierarchicalRunRuleBean ruleBean) {
        List<Serializable> args = new ArrayList<>();

        if (  RuleModelEnum.COMPARE.getCode()== ruleBean.getModel()) {
            args = buildFileOrScriptRuleArgs(infoBean, ruleBean);
        } else if(RuleITypeEnums.DIRECTORY.getCode().equals(ruleBean.getType()) && RuleModelEnum.SYNC.getCode()== ruleBean.getModel()){
            args = buildDirectoryRuleArgs(infoBean, ruleBean);
        }else{
            args = buildFileOrScriptRuleArgs(infoBean, ruleBean);
        }
        return args;
    }

    /**
     * 构建目录规则参数
     *
     * @param infoBean 实例详情Bean
     * @param ruleBean 规则Bean
     * @return 参数列表
     */
    private List<Serializable> buildDirectoryRuleArgs(HierarchicalRunInstanceInfoBean infoBean, HierarchicalRunRuleBean ruleBean) {
        List<Serializable> args = new ArrayList<>();
        
        String content = "";
        String way = "";
        if (RuleWayEnums.ALL.getCode().equals(ruleBean.getWay())) {
            way = "all";
        } else {
            way = "part";
            // 匹配
            if (ruleBean.getRuleContent() != null && ruleBean.getRuleContent().getContent() != null) {
                content = ruleBean.getRuleContent().getContent();
            }
            if (RuleMatchTypeEnums.MATCH.getCode().equals(ruleBean.getRuleType())) {
                content = "--include " + content;
            } else {
                content = "--exclude " + content;
            }
        }
        
        args.add("socket");
        args.add(way);
        args.add(ruleBean.getSourcePath());
        args.add(infoBean.getSourceComputerIp() + ":" + infoBean.getSourceComputerPort());
        args.add(ruleBean.getPath());
        args.add(infoBean.getTargetComputerIp() + ":" + infoBean.getTargetComputerPort());
        args.add(content);
        args.add(getScriptExtend(infoBean.getSourceComputerOs()));
        args.add("0");
        
        return args;
    }

    /**
     * 构建文件或脚本规则参数
     *
     * @param infoBean 实例详情Bean
     * @param ruleBean 规则Bean
     * @return 参数列表
     */
    private List<Serializable> buildFileOrScriptRuleArgs(HierarchicalRunInstanceInfoBean infoBean, HierarchicalRunRuleBean ruleBean) {
        List<Serializable> args = new ArrayList<>();
        
        String shString = getShStringForScript(ruleBean, infoBean);
        String checkContent = "";
        String excludeCheckContent = "";
        
        if (RuleWayEnums.PARTIAL.getCode().equals(ruleBean.getWay())) {
            String content = "";
            if (ruleBean.getRuleContent() != null) {
                content = ruleBean.getRuleContent().getContent();
            }
            if (RuleMatchTypeEnums.MATCH.getCode().equals(ruleBean.getRuleType())) {
                checkContent = content;
            } else {
                excludeCheckContent = content;
            }
        }
        
        args.add(infoBean.getSourceComputerIp() + ":" + infoBean.getSourceComputerPort());
        if(RuleITypeEnums.DIRECTORY.getCode().equals(ruleBean.getType())){
            args.add(ruleBean.getSourcePath());
        }else{
            args.add(shString + ruleBean.getSourcePath());
        }
        args.add(ruleBean.getEncode());
        args.add(RuleITypeEnums.getNameByCode(ruleBean.getType()));
        // 基线内容
        args.add("");
        args.add(checkContent);
        args.add(excludeCheckContent);
        args.add(infoBean.getTargetComputerIp() + ":" + infoBean.getTargetComputerPort());
        if(RuleITypeEnums.DIRECTORY.getCode().equals(ruleBean.getType())){
            args.add(ruleBean.getPath());
        } else{
            args.add(shString + ruleBean.getPath());
        }
        args.add(ruleBean.getEncode());
        args.add(RuleITypeEnums.getNameByCode(ruleBean.getType()));
        // 基线内容
        args.add("");
        args.add(checkContent);
        args.add(excludeCheckContent);
        args.add(false);
        
        return args;
    }

    /**
     * 获取脚本前缀字符串
     *
     * @param ruleBean 规则Bean
     * @param infoBean 实例详情Bean
     * @return sh前缀字符串
     */
    private String getShStringForScript(HierarchicalRunRuleBean ruleBean, HierarchicalRunInstanceInfoBean infoBean) {
        String shString = "";
        if (RuleITypeEnums.SCRIPT.getCode().equals(ruleBean.getType()) 
            && StringUtils.isNoneBlank(infoBean.getSourceComputerOs()) 
            && (infoBean.getSourceComputerOs().toLowerCase().contains("linux") 
                || infoBean.getSourceComputerOs().toLowerCase().contains("aix"))) {
            shString = "sh ";
        }
        return shString;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class,EngineServiceException.class})
    public boolean startContrastSendAndUpdateState(List<StartTaskFlowDto> startTaskFlowDtoList,String describe) throws EngineServiceException {
        if(startTaskFlowDtoList.isEmpty()){
            return false;
        }
        for (StartTaskFlowDto startTaskFlowDto : startTaskFlowDtoList) {
            logger.info("{}发送MQ消息到引擎消费MQ：{}", describe, JSON.toJSONString(startTaskFlowDto));
            startContrastBaseService.updateInstanceStatusToInitial(startTaskFlowDto.getUniqueTaskId());
            boolean sendFlag = sendDataToEngineMqProducer.sendStartTaskFlowData(startTaskFlowDto);
            if (!sendFlag) {
                logger.error("{}发送MQ消息到引擎消费MQ主题失败：{}", describe, startTaskFlowDto);
                throw new EngineServiceException("发送MQ消息到引擎消费MQ主题失败");
            }
        }
        return true;
    }


    public String getScriptExtend ( String iosName )
    {
        String extend = ".sh";
        if (StringUtils.isNotBlank(iosName) && iosName.toLowerCase().contains("win"))
        {
            extend = ".bat";
        }
        return extend;
    }
}
