package com.ideal.envc.enums;

import com.ideal.envc.model.enums.FileComparisonStrategy;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件比较策略枚举单元测试
 *
 * <AUTHOR>
 */
public class FileComparisonStrategyTest {

    @Test
    @DisplayName("测试枚举基本属性")
    void testEnumBasicProperties() {
        // 测试MD5_ONLY策略
        FileComparisonStrategy md5Only = FileComparisonStrategy.MD5_ONLY;
        assertEquals("MD5_ONLY", md5Only.getCode());
        assertEquals("仅MD5比较", md5Only.getName());
        assertEquals("只比较MD5值，MD5相同则认为一致", md5Only.getDescription());
        assertFalse(md5Only.isComprehensive());

        // 测试COMPREHENSIVE策略
        FileComparisonStrategy comprehensive = FileComparisonStrategy.COMPREHENSIVE;
        assertEquals("COMPREHENSIVE", comprehensive.getCode());
        assertEquals("综合比较", comprehensive.getName());
        assertEquals("比较文件大小、权限和MD5值，任何一个不同都认为不一致", comprehensive.getDescription());
        assertTrue(comprehensive.isComprehensive());
    }

    @Test
    @DisplayName("测试fromCode方法 - 有效代码")
    void testFromCode_ValidCodes() {
        // 测试有效的策略代码
        assertEquals(FileComparisonStrategy.MD5_ONLY, FileComparisonStrategy.fromCode("MD5_ONLY"));
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.fromCode("COMPREHENSIVE"));
    }

    @Test
    @DisplayName("测试fromCode方法 - 无效代码")
    void testFromCode_InvalidCodes() {
        // 测试无效的策略代码，应该返回默认策略
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.fromCode("INVALID_CODE"));
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.fromCode(""));
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.fromCode("unknown"));
    }

    @Test
    @DisplayName("测试fromCode方法 - null代码")
    void testFromCode_NullCode() {
        // 测试null代码，应该返回默认策略
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.fromCode(null));
    }

    @Test
    @DisplayName("测试isComprehensive方法")
    void testIsComprehensive() {
        // 测试MD5_ONLY策略
        assertFalse(FileComparisonStrategy.MD5_ONLY.isComprehensive());
        
        // 测试COMPREHENSIVE策略
        assertTrue(FileComparisonStrategy.COMPREHENSIVE.isComprehensive());
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        String md5OnlyString = FileComparisonStrategy.MD5_ONLY.toString();
        assertTrue(md5OnlyString.contains("MD5_ONLY"));
        assertTrue(md5OnlyString.contains("仅MD5比较"));
        assertTrue(md5OnlyString.contains("只比较MD5值，MD5相同则认为一致"));

        String comprehensiveString = FileComparisonStrategy.COMPREHENSIVE.toString();
        assertTrue(comprehensiveString.contains("COMPREHENSIVE"));
        assertTrue(comprehensiveString.contains("综合比较"));
        assertTrue(comprehensiveString.contains("比较文件大小、权限和MD5值，任何一个不同都认为不一致"));
    }

    @Test
    @DisplayName("测试枚举值数量")
    void testEnumValues() {
        FileComparisonStrategy[] values = FileComparisonStrategy.values();
        assertEquals(2, values.length, "应该有2个枚举值");
        
        // 验证包含的枚举值
        boolean hasMd5Only = false;
        boolean hasComprehensive = false;
        
        for (FileComparisonStrategy strategy : values) {
            if (strategy == FileComparisonStrategy.MD5_ONLY) {
                hasMd5Only = true;
            } else if (strategy == FileComparisonStrategy.COMPREHENSIVE) {
                hasComprehensive = true;
            }
        }
        
        assertTrue(hasMd5Only, "应该包含MD5_ONLY策略");
        assertTrue(hasComprehensive, "应该包含COMPREHENSIVE策略");
    }

    @Test
    @DisplayName("测试枚举valueOf方法")
    void testValueOf() {
        assertEquals(FileComparisonStrategy.MD5_ONLY, FileComparisonStrategy.valueOf("MD5_ONLY"));
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.valueOf("COMPREHENSIVE"));
        
        // 测试无效值应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            FileComparisonStrategy.valueOf("INVALID");
        });
    }

    @Test
    @DisplayName("测试枚举的不可变性")
    void testEnumImmutability() {
        // 枚举应该是不可变的，相同的枚举值应该是同一个实例
        assertSame(FileComparisonStrategy.MD5_ONLY, FileComparisonStrategy.valueOf("MD5_ONLY"));
        assertSame(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.valueOf("COMPREHENSIVE"));
        
        // 通过fromCode获取的也应该是同一个实例
        assertSame(FileComparisonStrategy.MD5_ONLY, FileComparisonStrategy.fromCode("MD5_ONLY"));
        assertSame(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.fromCode("COMPREHENSIVE"));
    }

    @Test
    @DisplayName("测试枚举比较")
    void testEnumComparison() {
        // 测试枚举相等性
        assertEquals(FileComparisonStrategy.MD5_ONLY, FileComparisonStrategy.MD5_ONLY);
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.COMPREHENSIVE);
        
        // 测试枚举不等性
        assertNotEquals(FileComparisonStrategy.MD5_ONLY, FileComparisonStrategy.COMPREHENSIVE);
        assertNotEquals(FileComparisonStrategy.COMPREHENSIVE, FileComparisonStrategy.MD5_ONLY);
        
        // 测试与null的比较
        assertNotEquals(FileComparisonStrategy.MD5_ONLY, null);
        assertNotEquals(FileComparisonStrategy.COMPREHENSIVE, null);
    }

    @Test
    @DisplayName("测试枚举在switch语句中的使用")
    void testEnumInSwitch() {
        // 测试枚举在switch语句中的使用
        String result1 = getStrategyDescription(FileComparisonStrategy.MD5_ONLY);
        assertEquals("MD5比较", result1);
        
        String result2 = getStrategyDescription(FileComparisonStrategy.COMPREHENSIVE);
        assertEquals("综合比较", result2);
    }

    /**
     * 辅助方法：根据策略返回描述（用于测试switch语句）
     */
    private String getStrategyDescription(FileComparisonStrategy strategy) {
        switch (strategy) {
            case MD5_ONLY:
                return "MD5比较";
            case COMPREHENSIVE:
                return "综合比较";
            default:
                return "未知策略";
        }
    }
}
