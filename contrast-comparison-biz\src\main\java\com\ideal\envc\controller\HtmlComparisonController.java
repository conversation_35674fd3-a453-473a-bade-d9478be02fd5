package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IHtmlComparisonService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * HTML比对控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htmlComparison")
@MethodPermission("@dp.hasBtnPermission('comparison-results')")
public class HtmlComparisonController {
    
    private static final Logger logger = LoggerFactory.getLogger(HtmlComparisonController.class);
    
    private final IHtmlComparisonService htmlComparisonService;
    private final UserinfoComponent userinfoComponent;
    
    public HtmlComparisonController(IHtmlComparisonService htmlComparisonService, 
                                  UserinfoComponent userinfoComponent) {
        this.htmlComparisonService = htmlComparisonService;
        this.userinfoComponent = userinfoComponent;
    }
    
    /**
     * 解析HTML比对内容
     *
     * @param request 比对请求参数
     * @return 比对结果
     */
    @PostMapping("/parse")
    public R<HtmlComparisonResultDto> parseHtmlComparison(@Valid @RequestBody HtmlComparisonRequestDto request) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            logger.info("用户{}开始解析HTML比对内容，flowId：{}，基线服务器：{}，目标服务器：{}",
                    userDto.getFullName(), request.getFlowId(), request.getBaselineServer(), request.getTargetServer());

            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            logger.info("HTML比对解析完成，flowId：{}，结果统计 - HTML行数：{}行，基线文件：{}个，目标文件：{}个，一致：{}个，不一致：{}个，缺失：{}个，多出：{}个",
                    request.getFlowId(), result.getTotalHtmlRows(), result.getTotalSourceFiles(), result.getTotalTargetFiles(),
                    result.getConsistentCount(), result.getInconsistentCount(),
                    result.getMissingCount(), result.getExtraCount());

            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), result, ResponseCodeEnum.SUCCESS.getDesc());

        } catch (ContrastBusinessException e) {
            logger.warn("HTML比对解析业务异常：{}", e.getMessage());
            // 根据异常消息判断具体的响应码
            if (e.getMessage().contains("流程ID不能为空")) {
                return R.fail(ResponseCodeEnum.FLOW_ID_EMPTY.getCode(), e.getMessage());
            } else if (e.getMessage().contains("流程结果不存在") || e.getMessage().contains("未查询到对应的流程结果数据")) {
                return R.fail(ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode(), e.getMessage());
            } else if (e.getMessage().contains("流程详情不存在") || e.getMessage().contains("未查询到对应设备相关信息")) {
                return R.fail(ResponseCodeEnum.FLOW_DETAIL_NOT_FOUND.getCode(), e.getMessage());
            } else if (e.getMessage().contains("流程内容为空") || e.getMessage().contains("流程结果内容为空")) {
                return R.fail(ResponseCodeEnum.FLOW_CONTENT_EMPTY.getCode(), e.getMessage());
            } else if (e.getMessage().contains("HTML内容为空")) {
                return R.fail(ResponseCodeEnum.HTML_CONTENT_EMPTY.getCode(), e.getMessage());
            } else if (e.getMessage().contains("HTML解析失败")) {
                return R.fail(ResponseCodeEnum.HTML_PARSE_FAIL.getCode(), e.getMessage());
            } else if (e.getMessage().contains("JSON解析失败")) {
                return R.fail(ResponseCodeEnum.JSON_PARSE_FAIL.getCode(), e.getMessage());
            } else if (e.getMessage().contains("其他解析失败")) {
                return R.fail(ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode(), e.getMessage());
            } else if (e.getMessage().contains("解析失败") || e.getMessage().contains("内容格式不正确")) {
                return R.fail(ResponseCodeEnum.CONTENT_PARSE_FAIL.getCode(), e.getMessage());
            } else {
                return R.fail(ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("HTML比对解析系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
    
    /**
     * 导出HTML比对结果到Excel
     *
     * @param flowId 流程ID
     * @param response HTTP响应对象
     */
    @PostMapping("/export")
    public R<Object> exportHtmlComparison(@RequestParam Long flowId,
                                   HttpServletResponse response) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            logger.info("用户{}开始导出HTML比对结果，flowId：{}",
                    userDto.getFullName(), flowId);

            // 构建请求对象
            HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
            request.setFlowId(flowId);

            htmlComparisonService.exportHtmlComparisonResult(request, response);

            logger.info("HTML比对结果导出完成，flowId：{}", flowId);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), true, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.warn("HTML比对结果导出业务异常：{}", e.getMessage());
            return handleExportError(response, e);
        } catch (Exception e) {
            logger.error("HTML比对结果导出系统异常", e);
            return  handleExportError(response, new ContrastBusinessException(ResponseCodeEnum.SYSTEM_ERROR.getDesc()));
        }
    }

    /**
     * 处理导出错误
     *
     * @param response HTTP响应对象
     * @param e 异常对象
     */
    private R<Object> handleExportError(HttpServletResponse response, ContrastBusinessException e) {
        try {
            String errorCode;
            String errorMessage = e.getMessage();

            // 根据异常消息判断具体的响应码
            if (errorMessage.contains("流程ID不能为空")) {
                errorCode = ResponseCodeEnum.FLOW_ID_EMPTY.getCode();
            } else if (errorMessage.contains("流程结果不存在") || errorMessage.contains("未查询到对应的流程结果数据")) {
                errorCode = ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode();
            } else if (errorMessage.contains("流程详情不存在") || errorMessage.contains("未查询到对应设备相关信息")) {
                errorCode = ResponseCodeEnum.FLOW_DETAIL_NOT_FOUND.getCode();
            } else if (errorMessage.contains("流程内容为空") || errorMessage.contains("流程结果内容为空")) {
                errorCode = ResponseCodeEnum.FLOW_CONTENT_EMPTY.getCode();
            } else if (errorMessage.contains("导出数据为空")) {
                errorCode = ResponseCodeEnum.EXPORT_DATA_EMPTY.getCode();
            } else if (errorMessage.contains("Excel导出失败")) {
                errorCode = ResponseCodeEnum.EXCEL_EXPORT_FAIL.getCode();
            } else if (errorMessage.contains("文件写入失败")) {
                errorCode = ResponseCodeEnum.FILE_WRITE_FAIL.getCode();
            } else if (errorMessage.contains("权限不足")) {
                errorCode = ResponseCodeEnum.EXPORT_PERMISSION_DENIED.getCode();
            } else if (errorMessage.contains("系统异常")) {
                errorCode = ResponseCodeEnum.SYSTEM_ERROR.getCode();
            } else {
                errorCode = ResponseCodeEnum.EXPORT_FAIL.getCode();
            }
            return R.fail(errorCode,errorMessage);

        } catch (Exception ex) {
            logger.error("写入错误响应失败", ex);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
    
    /**
     * 快速解析HTML比对内容（简化版接口，用于测试）
     *
     * @param request 比对请求参数
     * @return 比对结果摘要
     */
    @PostMapping("/quickParse")
    public R<String> quickParseHtmlComparison(@Valid @RequestBody HtmlComparisonRequestDto request) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            logger.info("用户{}开始快速解析HTML比对内容，flowId：{}",
                    userDto.getFullName(), request.getFlowId());

            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            // 构建摘要信息
            StringBuilder summary = new StringBuilder();
            summary.append("HTML比对解析完成！");
            summary.append("解析HTML行数：").append(result.getTotalHtmlRows()).append("行；");
            summary.append("基线文件：").append(result.getTotalSourceFiles()).append("个，");
            summary.append("目标文件：").append(result.getTotalTargetFiles()).append("个；");
            summary.append("一致：").append(result.getConsistentCount()).append("个（")
                   .append(result.getConsistentRate()).append("%），");
            summary.append("不一致：").append(result.getInconsistentCount()).append("个（")
                   .append(result.getInconsistentRate()).append("%），");
            summary.append("缺失：").append(result.getMissingCount()).append("个（")
                   .append(result.getMissingRate()).append("%），");
            summary.append("多出：").append(result.getExtraCount()).append("个（")
                   .append(result.getExtraRate()).append("%）");

            logger.info("快速HTML比对解析完成，flowId：{}，摘要：{}",
                       request.getFlowId(), summary.toString());

            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), summary.toString(), ResponseCodeEnum.SUCCESS.getDesc());

        } catch (ContrastBusinessException e) {
            logger.warn("快速HTML比对解析业务异常：{}", e.getMessage());
            return getBusinessExceptionResponse(e);
        } catch (Exception e) {
            logger.error("快速HTML比对解析系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
    
    /**
     * 获取HTML比对建议
     *
     * @param request 比对请求参数
     * @return 比对建议
     */
    @PostMapping("/advice")
    public R<String> getComparisonAdvice(@Valid @RequestBody HtmlComparisonRequestDto request) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            logger.info("用户{}请求HTML比对建议，flowId：{}",
                    userDto.getFullName(), request.getFlowId());

            HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);

            // 生成建议
            String advice = generateAdvice(result);

            logger.info("HTML比对建议生成完成，flowId：{}", request.getFlowId());

            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), advice, ResponseCodeEnum.SUCCESS.getDesc());

        } catch (ContrastBusinessException e) {
            logger.warn("获取HTML比对建议业务异常：{}", e.getMessage());
            return getBusinessExceptionResponse(e);
        } catch (Exception e) {
            logger.error("获取HTML比对建议系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 根据业务异常获取统一的响应
     *
     * @param e 业务异常
     * @return 统一响应
     */
    private R<String> getBusinessExceptionResponse(ContrastBusinessException e) {
        String message = e.getMessage();

        if (message.contains("流程ID不能为空")) {
            return R.fail(ResponseCodeEnum.FLOW_ID_EMPTY.getCode(), message);
        } else if (message.contains("流程结果不存在") || message.contains("未查询到对应的流程结果数据")) {
            return R.fail(ResponseCodeEnum.FLOW_RESULT_NOT_FOUND.getCode(), message);
        } else if (message.contains("流程详情不存在") || message.contains("未查询到对应设备相关信息")) {
            return R.fail(ResponseCodeEnum.FLOW_DETAIL_NOT_FOUND.getCode(), message);
        } else if (message.contains("流程内容为空") || message.contains("流程结果内容为空")) {
            return R.fail(ResponseCodeEnum.FLOW_CONTENT_EMPTY.getCode(), message);
        } else if (message.contains("HTML内容为空")) {
            return R.fail(ResponseCodeEnum.HTML_CONTENT_EMPTY.getCode(), message);
        } else if (message.contains("HTML解析失败")) {
            return R.fail(ResponseCodeEnum.HTML_PARSE_FAIL.getCode(), message);
        } else if (message.contains("JSON解析失败")) {
            return R.fail(ResponseCodeEnum.JSON_PARSE_FAIL.getCode(), message);
        } else if (message.contains("其他解析失败")) {
            return R.fail(ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode(), message);
        } else if (message.contains("解析失败") || message.contains("内容格式不正确")) {
            return R.fail(ResponseCodeEnum.CONTENT_PARSE_FAIL.getCode(), message);
        } else {
            return R.fail(ResponseCodeEnum.COMPARISON_PARSE_FAIL.getCode(), message);
        }
    }

    /**
     * 生成比对建议
     *
     * @param result 比对结果
     * @return 建议信息
     */
    private String generateAdvice(HtmlComparisonResultDto result) {
        if (result == null) {
            return "无法提供建议：比对结果为空";
        }

        int totalFiles = result.getTotalSourceFiles();
        int inconsistentCount = result.getInconsistentCount();
        int missingCount = result.getMissingCount();
        
        if (totalFiles == 0) {
            return "无文件需要比对";
        }

        double inconsistentRate = inconsistentCount * 100.0 / totalFiles;
        double missingRate = missingCount * 100.0 / totalFiles;
        double totalDiffRate = inconsistentRate + missingRate;

        StringBuilder advice = new StringBuilder();
        
        if (totalDiffRate > 50) {
            advice.append("🚨 差异率超过50%，建议：\n");
            advice.append("1. 全面检查环境配置和部署流程\n");
            advice.append("2. 验证基线环境和目标环境的一致性\n");
            advice.append("3. 检查是否存在版本差异或配置错误");
        } else if (totalDiffRate > 20) {
            advice.append("⚠️ 差异率在20-50%之间，建议：\n");
            advice.append("1. 重点关注关键文件和配置\n");
            advice.append("2. 检查不一致文件的具体差异原因\n");
            advice.append("3. 确认差异是否符合预期");
        } else if (totalDiffRate > 5) {
            advice.append("✅ 差异率在5-20%之间，建议：\n");
            advice.append("1. 环境基本一致，检查少量差异文件\n");
            advice.append("2. 确认差异文件是否为正常的环境差异\n");
            advice.append("3. 可以考虑进行部署或切换");
        } else {
            advice.append("✅ 差异率低于5%，建议：\n");
            advice.append("1. 环境高度一致，可以放心使用\n");
            advice.append("2. 少量差异可能为正常的配置差异\n");
            advice.append("3. 建议进行最终验证后投入使用");
        }
        
        // 添加具体统计信息
        advice.append("\n\n📊 详细统计：\n");
        advice.append("- 解析HTML行数：").append(result.getTotalHtmlRows()).append("行\n");
        advice.append("- 基线文件总数：").append(result.getTotalSourceFiles()).append("个\n");
        advice.append("- 目标文件总数：").append(result.getTotalTargetFiles()).append("个\n");
        advice.append("- 一致文件：").append(result.getConsistentCount()).append("个（")
               .append(result.getConsistentRate()).append("%）\n");
        advice.append("- 不一致文件：").append(result.getInconsistentCount()).append("个（")
               .append(result.getInconsistentRate()).append("%）\n");
        advice.append("- 缺失文件：").append(result.getMissingCount()).append("个（")
               .append(result.getMissingRate()).append("%）\n");
        advice.append("- 多出文件：").append(result.getExtraCount()).append("个（")
               .append(result.getExtraRate()).append("%）");

        return advice.toString();
    }
}
