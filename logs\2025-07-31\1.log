2025-07-31 17:01:05 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753952465714] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 17:01:05 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753952465714] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 17:01:06 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753952465714] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-07-31 17:01:06 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-07-31 18:01:06 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753956066093] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 18:01:06 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753956066093] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 18:01:06 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753956066093] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-07-31 18:01:06 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-07-31 19:01:05 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753959665444] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 19:01:05 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753959665444] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 19:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753959665444] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-07-31 19:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-07-31 20:01:06 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753963266192] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 20:01:06 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753963266192] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 20:01:06 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753963266192] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-07-31 20:01:06 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-07-31 21:01:06 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753966866408] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 21:01:06 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753966866408] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 21:01:06 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753966866408] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-07-31 21:01:06 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-07-31 22:01:05 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753970465855] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 22:01:05 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753970465855] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 22:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753970465855] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-07-31 22:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-07-31 23:01:05 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-635-1753974065822] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 23:01:05 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-635-1753974065822] receive schedule job have to execute ,task has params : {"createName":"卢长海","creatorId":1082170050173710336,"cron":"0 10 * * * ? *","jobHandlerName":"contrastJobHandler","planId":1109651830540562432,"scheduleJobId":635,"taskId":1110815670843023360,"taskName":"对比任务_1110815670843023360"}
2025-07-31 23:01:05 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-635-1753974065822] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-07-31 23:01:05 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
