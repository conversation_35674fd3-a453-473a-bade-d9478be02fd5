# HTML比对功能使用说明

## 概述

HTML比对功能是一个专门用于解析环境比对结果的组件，通过流程ID（flowId）查询比对内容，能够从HTML或JSON格式的内容中提取源服务器和目标服务器的文件信息，进行比对分析，并生成Excel格式的比对报表。

## 业务流程

### 数据流转过程
1. **前台传入flowId**：用户通过API传入流程ID
2. **查询流程结果**：根据flowId查询`ieai_envc_run_flow_result`表获取比对内容
3. **查询流程详情**：根据flowId查询流程详情获取服务器信息
4. **解析内容**：将查询到的content解析为`ContentCustomDto`对象
5. **提取HTML内容**：从`ContentCustomDto.content`属性中获取真正的比对数据
6. **智能解析**：自动识别数据格式（HTML/JSON/纯文本）并选择合适的解析策略
7. **生成报表**：统计分析并生成Excel格式的比对报表

### 核心查询方法
```java
// 查询流程结果
RunFlowResultEntity runFlowResult = runFlowResultMapper.selectRunFlowResultByFlowId(flowId);

// 查询流程详情
List<RunFlowDetailBean> runFlowDetailBeanList = runRuleMapper.selectRunRuleDetailByFlowId(flowId);

// 解析内容
String content = getFlowContent(runFlowResult, flowId);
ContentCustomDto contentCustomDto = JSON.parseObject(content, ContentCustomDto.class);

// 获取真正的HTML内容
String htmlContent = contentCustomDto.getContent();
```

## 功能特点

### 1. 智能HTML解析
- **双重解析策略**：优先使用dom4j解析，失败时自动切换到正则表达式备用方案
- **HTML预处理**：自动处理HTML格式问题，确保解析成功率
- **内容解码**：自动进行HTML实体解码，还原真实文件内容

### 2. 状态识别
- **一致**：源和目标服务器文件完全相同
- **不一致**：文件存在但内容不同（大小、权限、MD5等）
- **缺失**：源服务器有但目标服务器没有的文件
- **多出**：目标服务器有但源服务器没有的文件

### 3. 统计分析
- 文件数量统计
- 各类差异比率计算
- 智能建议生成

### 4. Excel导出
- 复用现有的文件比对Excel导出功能
- 支持详细的比对报表格式
- 包含统计汇总和详细文件列表

## 架构设计

### 分层架构
```
Controller Layer (控制器层)
    ↓
Component Layer (组件层) 
    ↓
Service Layer (服务层)
    ↓
Util Layer (工具层)
    ↓
DTO Layer (数据传输层)
```

### 核心类结构

#### DTO类（数据传输对象）
- **HtmlComparisonRequestDto**: HTML比对请求DTO
- **HtmlComparisonResultDto**: HTML比对结果DTO
- **FileInfoDto**: 文件信息DTO（复用现有）

#### 服务层
- **IHtmlComparisonService**: HTML比对服务接口
- **HtmlComparisonServiceImpl**: 服务实现类

#### 组件层
- **HtmlComparisonComponent**: 核心组件类，提供简洁的API

#### 工具层
- **HtmlComparisonParser**: HTML解析工具类

#### 控制器层
- **HtmlComparisonController**: REST API控制器

## 使用方式

### 1. 组件方式调用

```java
@Autowired
private HtmlComparisonComponent htmlComparisonComponent;

// 基本解析（通过flowId）
HtmlComparisonResultDto result = htmlComparisonComponent.parseHtmlComparison(flowId);

// 带服务器信息的解析（可选参数，会从流程详情中自动获取）
HtmlComparisonResultDto result = htmlComparisonComponent.parseHtmlComparison(
    flowId, "基线服务器", "目标服务器");

// 带完整信息的解析
HtmlComparisonResultDto result = htmlComparisonComponent.parseHtmlComparison(
    flowId, "基线服务器", "目标服务器", "比对描述");

// 解析并导出Excel
HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
request.setFlowId(flowId);
request.setBaselineServer("基线服务器"); // 可选，会从流程详情中获取
request.setTargetServer("目标服务器");   // 可选，会从流程详情中获取
htmlComparisonComponent.parseAndExport(request, response);
```

### 2. REST API调用

#### 解析HTML比对内容
```http
POST /htmlComparison/parse
Content-Type: application/json

{
    "flowId": 12345,
    "baselineServer": "基线服务器",  // 可选，会从流程详情中获取
    "targetServer": "目标服务器",    // 可选，会从流程详情中获取
    "description": "比对描述"        // 可选，会自动生成
}
```

#### 导出Excel
```http
POST /htmlComparison/export
Content-Type: application/json

{
    "flowId": 12345,
    "baselineServer": "基线服务器",  // 可选
    "targetServer": "目标服务器"     // 可选
}
```

#### 快速解析（返回摘要）
```http
POST /htmlComparison/quickParse
Content-Type: application/json

{
    "flowId": 12345
}
```

#### 获取比对建议
```http
POST /htmlComparison/advice
Content-Type: application/json

{
    "flowId": 12345
}
```

### 3. 服务层调用

```java
@Autowired
private IHtmlComparisonService htmlComparisonService;

HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
request.setHtmlContent(htmlContent);

HtmlComparisonResultDto result = htmlComparisonService.parseHtmlComparison(request);
```

## 支持的数据格式

系统支持多种数据格式的自动识别和解析：

### 1. HTML格式（原始设计格式）
```html
<div class="comparison_space">
    <table cellpadding="0" cellspacing="0" border="0" class="comparison_tab">
        <tr>
            <td class="cp_frame warning cpi_td_w">
                <table cellpadding="0" cellspacing="0" border="0">
                    <tr>
                        <td><div class="cp_icon icon_pos3"></div></td>
                        <td><div class="cp_text">1</div></td>
                        <td><div class="cp_cn">COPYRIGHT (size: 3.17 KB, permissions: -rwxrwxr-x, MD5: a762796b2a8989b8952b653a178607a1)</div></td>
                    </tr>
                </table>
            </td>
            <td width="5"></td>
            <td class="cp_line"></td>
            <td width="5"></td>
            <td class="cp_frame warning cpi_td_w">
                <table cellpadding="0" cellspacing="0" border="0">
                    <tr>
                        <td><div class="cp_icon icon_pos3"></div></td>
                        <td><div class="cp_text">1</div></td>
                        <td><div class="cp_cn">COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)</div></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
```

**CSS类名映射**：
- `cp_frame cpi_td_w`：一致
- `cp_frame warning cpi_td_w`：不一致
- `cp_frame abnormal cpi_td_w`：缺失
- `cp_frame complete cpi_td_w`：多出

### 2. JSON格式（推荐使用）
```json
{
    "sourceContent": "COPYRIGHT (size: 3.17 KB, permissions: -rwxrwxr-x, MD5: a762796b2a8989b8952b653a178607a1)\nLICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87c4d77e0e91a6dbc15f)\nREADME (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)\nrelease (size: 424.00 B, permissions: -rwxrwxr-x, MD5: 994c1876d190830b199d08d4b90430a7)",
    "targetContent": "COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)\nLICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87c4d77e0e91a6dbc15f)\nREADME (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)\nrelease (size: 424.00 B, permissions: -rw-r--r--, MD5: 994c1876d190830b199d08d4b90430a7)"
}
```

### 3. 纯文本格式
```
COPYRIGHT (size: 3.17 KB, permissions: -rwxrwxr-x, MD5: a762796b2a8989b8952b653a178607a1)
LICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87c4d77e0e91a6dbc15f)
README (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)
release (size: 424.00 B, permissions: -rwxrwxr-x, MD5: 994c1876d190830b199d08d4b90430a7)
```

### 文件信息格式规范
所有格式中的文件信息都遵循以下规范：
```
文件名 (size: 大小, permissions: 权限, MD5: MD5值)
```

**示例**：
```
COPYRIGHT (size: 3.17 KB, permissions: -rwxrwxr-x, MD5: a762796b2a8989b8952b653a178607a1)
bin/java (size: 8.27 KB, permissions: -rw-r--r--, MD5: 9d5432654f4567e4e5076e6498471e8b)
lib/rt.jar (size: 63.26 MB, permissions: -rw-r--r--, MD5: b327c58617528acdba6965789fb1aeba)
```

## 返回结果

### HtmlComparisonResultDto 结构

```json
{
    "baselineServer": "基线服务器",
    "targetServer": "目标服务器",
    "description": "比对描述",
    "totalHtmlRows": 10,
    "totalSourceFiles": 100,
    "totalTargetFiles": 95,
    "consistentCount": 80,
    "inconsistentCount": 10,
    "missingCount": 10,
    "extraCount": 5,
    "consistentRate": 80.00,
    "inconsistentRate": 10.00,
    "missingRate": 10.00,
    "extraRate": 5.26,
    "consistentFiles": [...],
    "inconsistentFiles": [...],
    "missingFiles": [...],
    "extraFiles": [...]
}
```

## 智能建议

系统会根据差异率自动生成建议：

- **差异率 > 50%**: 🚨 建议全面检查环境配置
- **差异率 20-50%**: ⚠️ 建议重点关注关键文件
- **差异率 5-20%**: ✅ 环境基本一致，检查少量差异文件
- **差异率 < 5%**: ✅ 环境高度一致，可以放心使用

## 格式自动识别

系统具备智能格式识别功能，能够自动判断输入数据的格式类型：

### 识别逻辑
1. **HTML格式识别**：检查内容是否以`<`开头且包含HTML标签（`<div>`、`<table>`、`<tr>`等）
2. **JSON格式识别**：检查内容是否包含`"sourceContent"`和`"targetContent"`字段
3. **纯文本格式**：其他情况默认为纯文本格式

### 处理策略
- **HTML格式**：使用dom4j解析器 + 正则表达式备用方案
- **JSON格式**：使用FastJSON解析，然后调用现有的文件比对服务
- **纯文本格式**：直接作为源内容处理，调用现有的文件比对服务

### 优势
- **无需手动指定格式**：系统自动识别，用户无需关心数据格式
- **向后兼容**：支持原有的HTML格式，同时扩展支持新格式
- **统一接口**：不同格式使用相同的API接口，简化使用

## 技术特点

### 1. 高可靠性
- 多格式自动识别和解析
- 双重解析策略确保解析成功率
- 完善的异常处理机制
- 支持各种数据格式变体

### 2. 高性能
- 使用dom4j进行高效XML/HTML解析
- FastJSON高性能JSON解析
- 内存占用优化
- 支持大量数据处理

### 3. 易用性
- 智能格式识别，无需手动指定
- 简洁的API设计
- 多种调用方式
- 详细的错误信息和日志

### 4. 可扩展性
- 分层架构设计
- 接口与实现分离
- 支持新格式扩展
- 复用现有组件

## 依赖要求

### Maven依赖
```xml
<!-- dom4j用于HTML/XML解析 -->
<dependency>
    <groupId>org.dom4j</groupId>
    <artifactId>dom4j</artifactId>
    <version>2.1.3</version>
</dependency>
```

### 其他依赖
- commons-lang3（HTML编码/解码）
- 现有的文件比对服务（Excel导出）

## 使用示例

详细的使用示例请参考：
- `HtmlComparisonDemo.java` - 完整的功能演示
- 单元测试用例
- 集成测试示例

## 注意事项

1. **HTML格式**：确保HTML内容符合预期的结构格式
2. **编码问题**：HTML内容中的特殊字符会自动进行解码处理
3. **性能考虑**：大量数据时建议分批处理
4. **错误处理**：注意捕获和处理解析异常

## 未来扩展

1. **支持更多HTML格式**：扩展解析器支持更多的HTML结构
2. **自定义解析规则**：允许用户配置解析规则
3. **批量处理**：支持批量HTML文件处理
4. **可视化展示**：提供Web界面展示比对结果

这个HTML比对功能为环境一致性检查提供了强大而灵活的解决方案，能够有效提升运维效率和准确性。
