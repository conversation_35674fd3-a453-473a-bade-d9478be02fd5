package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.ContentDetailDto;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.ResultMonitorDto;
import com.ideal.envc.model.dto.ResultMonitorQueryDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.model.enums.StartFromEnums;
import com.ideal.envc.service.IResultMonitorService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 比对结果监控Controller测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ResultMonitorControllerTest {

    @Mock
    private IResultMonitorService resultMonitorService;

    @InjectMocks
    private ResultMonitorController resultMonitorController;

    private TableQueryDto<ResultMonitorQueryDto> tableQueryDto;
    private ResultMonitorQueryDto queryParam;
    private PageInfo<ResultMonitorDto> pageInfo;
    private List<ResultMonitorDto> resultList;
    private ResultMonitorDto resultMonitorDto;
    private ContentDetailDto contentDetailDto;

    @BeforeEach
    void setUp() {
        // 初始化查询参数
        queryParam = new ResultMonitorQueryDto();
        queryParam.setBusinessSystemName("测试系统");
        queryParam.setModel(1);
        queryParam.setResult(0);
        queryParam.setFrom(StartFromEnums.MANUAL_TRIGGER.getCode());

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(queryParam);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        // 初始化查询结果
        resultMonitorDto = new ResultMonitorDto();
        resultMonitorDto.setId(1L);
        resultMonitorDto.setBusinessSystemId(100L);
        resultMonitorDto.setBusinessSystemName("测试系统");
        resultMonitorDto.setModel(1);
        resultMonitorDto.setCreateTime(new Date());
        resultMonitorDto.setSourceComputerIp("***********");
        resultMonitorDto.setTargetComputerIp("***********");
        resultMonitorDto.setPath("/test/path");
        resultMonitorDto.setElapsedTimeStr("10分30秒");
        resultMonitorDto.setResult(0);
        resultMonitorDto.setState(1);
        resultMonitorDto.setFrom(StartFromEnums.MANUAL_TRIGGER.getCode());
        resultMonitorDto.setTriggerFrom(StartFromEnums.MANUAL_TRIGGER.getName());

        resultList = new ArrayList<>();
        resultList.add(resultMonitorDto);

        pageInfo = new PageInfo<>();
        pageInfo.setList(resultList);
        pageInfo.setTotal(1L);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        pageInfo.setPages(1);
        
        // 初始化内容详情
        contentDetailDto = new ContentDetailDto();
        contentDetailDto.setSourceContent("源内容");
        contentDetailDto.setTargetContent("目标内容");
    }

    @Test
    @DisplayName("测试查询比对结果列表")
    void testList() {
        // 设置Mock行为
        doReturn(pageInfo).when(resultMonitorService).selectResultMonitorList(
                any(ResultMonitorQueryDto.class),
                anyInt(),
                anyInt()
        );

        // 执行测试方法
        R<PageInfo<ResultMonitorDto>> response = resultMonitorController.list(tableQueryDto);

        // 验证结果
        assertNotNull(response);
        assertEquals("10000", response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().getList().size());
        assertEquals("测试系统", response.getData().getList().get(0).getBusinessSystemName());
        assertEquals("10分30秒", response.getData().getList().get(0).getElapsedTimeStr());

        // 验证调用服务方法的参数
        verify(resultMonitorService, times(1)).selectResultMonitorList(
                eq(queryParam),
                eq(1),
                eq(10)
        );
    }
    
    @Test
    @DisplayName("测试查询比对详情 - 成功场景")
    void testDetail_Success() throws ContrastBusinessException {
        // 设置Mock行为
        ContentDetailDto mockContentDetail = new ContentDetailDto();
        mockContentDetail.setSourceContent("123");
        mockContentDetail.setTargetContent("123456");
        doReturn(mockContentDetail).when(resultMonitorService).selectContentDetailByFlowId(anyLong());
        
        // 执行测试方法
        R<ContentDetailDto> response = resultMonitorController.detail(1L);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertNotNull(response.getData());
        assertEquals("123", response.getData().getSourceContent());
        assertEquals("123456", response.getData().getTargetContent());
        
        // 验证调用服务方法
        verify(resultMonitorService, times(1)).selectContentDetailByFlowId(eq(1L));
    }
    
    @Test
    @DisplayName("测试查询比对详情 - 系统异常")
    void testDetail_SystemError() throws ContrastBusinessException {
        // 模拟异常
        RuntimeException exception = new RuntimeException("系统异常");

        // 设置Mock行为
        doThrow(exception).when(resultMonitorService).selectContentDetailByFlowId(anyLong());

        // 执行测试方法
        R<ContentDetailDto> response = resultMonitorController.detail(1L);

        // 验证结果
        assertNotNull(response);
        assertEquals("139900", response.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), response.getMessage());
    }

    @Test
    @DisplayName("测试查询比对详情 - 业务异常")
    void testDetail_BusinessException() throws ContrastBusinessException {
        // 模拟业务异常
        ContrastBusinessException exception = new ContrastBusinessException("数据不存在");

        // 设置Mock行为
        doThrow(exception).when(resultMonitorService).selectContentDetailByFlowId(anyLong());

        // 执行测试方法
        R<ContentDetailDto> response = resultMonitorController.detail(1L);

        // 验证结果
        assertNotNull(response);
        assertEquals(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), response.getCode());
        assertEquals("数据不存在", response.getMessage());

        // 验证调用服务方法
        verify(resultMonitorService, times(1)).selectContentDetailByFlowId(eq(1L));
    }

    @Test
    @DisplayName("测试导出比对报表 - 成功场景")
    void testExportReport_Success() throws ContrastBusinessException {
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 设置Mock行为 - void方法使用doNothing
        doNothing().when(resultMonitorService).exportComparisonReportByFlowId(anyLong(), any(HttpServletResponse.class));

        // 执行测试方法
        R<Object> result = resultMonitorController.exportReport(1L, response);

        // 验证返回值
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(true, result.getData());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMsg());

        // 验证调用服务方法
        verify(resultMonitorService, times(1)).exportComparisonReportByFlowId(eq(1L), eq(response));
    }

    @Test
    @DisplayName("测试导出比对报表 - 业务异常")
    void testExportReport_BusinessException() throws ContrastBusinessException {
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 模拟业务异常
        ContrastBusinessException exception = new ContrastBusinessException("导出失败");
        doThrow(exception).when(resultMonitorService).exportComparisonReportByFlowId(anyLong(), any(HttpServletResponse.class));

        // 执行测试方法
        R<Object> result = resultMonitorController.exportReport(1L, response);

        // 验证返回值
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.EXPORT_FAIL.getCode(), result.getCode());
        assertEquals("导出失败", result.getMsg());

        // 验证调用服务方法
        verify(resultMonitorService, times(1)).exportComparisonReportByFlowId(eq(1L), eq(response));
    }

    @Test
    @DisplayName("测试导出比对报表 - 系统异常")
    void testExportReport_SystemException() throws ContrastBusinessException {
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 模拟系统异常
        RuntimeException exception = new RuntimeException("系统异常");
        doThrow(exception).when(resultMonitorService).exportComparisonReportByFlowId(anyLong(), any(HttpServletResponse.class));

        // 执行测试方法
        R<Object> result = resultMonitorController.exportReport(1L, response);

        // 验证返回值
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.EXPORT_FAIL.getCode(), result.getCode());
        assertEquals("系统异常", result.getMsg());

        // 验证调用服务方法
        verify(resultMonitorService, times(1)).exportComparisonReportByFlowId(eq(1L), eq(response));
    }

    @Test
    @DisplayName("测试导出比对报表 - 写入响应异常")
    void testExportReport_WriteResponseException() throws Exception {
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);

        // 模拟业务异常
        ContrastBusinessException businessException = new ContrastBusinessException("导出失败");
        doThrow(businessException).when(resultMonitorService).exportComparisonReportByFlowId(anyLong(), any(HttpServletResponse.class));

        // 执行测试方法
        R<Object> result = resultMonitorController.exportReport(1L, mockResponse);

        // 验证返回值
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.EXPORT_FAIL.getCode(), result.getCode());
        assertEquals("导出失败", result.getMsg());

        // 验证调用
        verify(resultMonitorService, times(1)).exportComparisonReportByFlowId(eq(1L), eq(mockResponse));
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 空查询参数")
    void testList_EmptyQueryParam() {
        TableQueryDto<ResultMonitorQueryDto> emptyTableQueryDto = new TableQueryDto<>();
        emptyTableQueryDto.setQueryParam(null);
        emptyTableQueryDto.setPageNum(1);
        emptyTableQueryDto.setPageSize(10);

        PageInfo<ResultMonitorDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(new ArrayList<>());
        emptyPageInfo.setTotal(0L);

        // 设置Mock行为
        doReturn(emptyPageInfo).when(resultMonitorService).selectResultMonitorList(
                any(),
                anyInt(),
                anyInt()
        );

        // 执行测试方法
        R<PageInfo<ResultMonitorDto>> response = resultMonitorController.list(emptyTableQueryDto);

        // 验证结果
        assertNotNull(response);
        assertEquals("10000", response.getCode());
        assertNotNull(response.getData());
        assertEquals(0, response.getData().getList().size());
        assertEquals(0L, response.getData().getTotal());

        // 验证调用服务方法
        verify(resultMonitorService, times(1)).selectResultMonitorList(
                eq(null),
                eq(1),
                eq(10)
        );
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 大数据量")
    void testList_LargeDataSet() {
        // 创建大数据量的测试数据
        List<ResultMonitorDto> largeResultList = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            ResultMonitorDto dto = new ResultMonitorDto();
            dto.setId((long) i);
            dto.setBusinessSystemName("测试系统" + i);
            dto.setModel(i % 3);
            dto.setResult(i % 2);
            largeResultList.add(dto);
        }

        PageInfo<ResultMonitorDto> largePageInfo = new PageInfo<>();
        largePageInfo.setList(largeResultList);
        largePageInfo.setTotal(100L);
        largePageInfo.setPageNum(1);
        largePageInfo.setPageSize(100);
        largePageInfo.setPages(1);

        // 设置Mock行为
        doReturn(largePageInfo).when(resultMonitorService).selectResultMonitorList(
                any(ResultMonitorQueryDto.class),
                anyInt(),
                anyInt()
        );

        // 执行测试方法
        R<PageInfo<ResultMonitorDto>> response = resultMonitorController.list(tableQueryDto);

        // 验证结果
        assertNotNull(response);
        assertEquals("10000", response.getCode());
        assertNotNull(response.getData());
        assertEquals(100, response.getData().getList().size());
        assertEquals(100L, response.getData().getTotal());

        // 验证调用服务方法
        verify(resultMonitorService, times(1)).selectResultMonitorList(
                eq(queryParam),
                eq(1),
                eq(10)
        );
    }

    @Test
    @DisplayName("测试构造函数")
    void testConstructor() {
        // 创建新的控制器实例
        ResultMonitorController controller = new ResultMonitorController(resultMonitorService);

        // 验证实例创建成功
        assertNotNull(controller);
    }

}