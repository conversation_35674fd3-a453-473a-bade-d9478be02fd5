package com.ideal.envc.component;

import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;
import com.ideal.envc.service.IHtmlComparisonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpServletResponse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * HtmlComparisonComponent的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("HtmlComparisonComponent单元测试")
class HtmlComparisonComponentTest {

    @Mock
    private IHtmlComparisonService htmlComparisonService;

    @Mock
    private HttpServletResponse response;

    @InjectMocks
    private HtmlComparisonComponent htmlComparisonComponent;

    private HtmlComparisonResultDto resultDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        resultDto = new HtmlComparisonResultDto();
        resultDto.setTotalSourceFiles(10);
        resultDto.setTotalTargetFiles(10);
        resultDto.setConsistentCount(8);
        resultDto.setInconsistentCount(1);
        resultDto.setMissingCount(1);
        resultDto.setExtraCount(0);
        resultDto.setTotalHtmlRows(100);
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 通过flowId")
    void testParseHtmlComparison_ByFlowId() throws ContrastBusinessException {
        Long flowId = 123L;
        
        // 设置Mock行为
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class))).thenReturn(resultDto);

        // 执行测试方法
        HtmlComparisonResultDto result = htmlComparisonComponent.parseHtmlComparison(flowId);

        // 验证结果
        assertNotNull(result);
        assertEquals(resultDto, result);

        // 验证服务调用
        verify(htmlComparisonService).parseHtmlComparison(any(HtmlComparisonRequestDto.class));
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - flowId为null")
    void testParseHtmlComparison_NullFlowId() throws ContrastBusinessException {
        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, 
            () -> htmlComparisonComponent.parseHtmlComparison(null));

        // 验证异常信息
        assertTrue(exception.getMessage().contains("flowId不能为空"));

        // 验证服务未被调用
        verify(htmlComparisonService, never()).parseHtmlComparison(any(HtmlComparisonRequestDto.class));
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 带服务器信息")
    void testParseHtmlComparison_WithServerInfo() throws ContrastBusinessException {
        Long flowId = 123L;
        String baselineServer = "基线服务器";
        String targetServer = "目标服务器";
        
        // 设置Mock行为
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class))).thenReturn(resultDto);

        // 执行测试方法
        HtmlComparisonResultDto result = htmlComparisonComponent.parseHtmlComparison(flowId, baselineServer, targetServer);

        // 验证结果
        assertNotNull(result);
        assertEquals(resultDto, result);

        // 验证服务调用
        verify(htmlComparisonService).parseHtmlComparison(any(HtmlComparisonRequestDto.class));
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 带服务器信息但flowId为null")
    void testParseHtmlComparison_WithServerInfo_NullFlowId() throws ContrastBusinessException {
        String baselineServer = "基线服务器";
        String targetServer = "目标服务器";

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, 
            () -> htmlComparisonComponent.parseHtmlComparison(null, baselineServer, targetServer));

        // 验证异常信息
        assertTrue(exception.getMessage().contains("flowId不能为空"));

        // 验证服务未被调用
        verify(htmlComparisonService, never()).parseHtmlComparison(any(HtmlComparisonRequestDto.class));
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 带完整信息")
    void testParseHtmlComparison_WithFullInfo() throws ContrastBusinessException {
        Long flowId = 123L;
        String baselineServer = "基线服务器";
        String targetServer = "目标服务器";
        String description = "测试描述";
        
        // 设置Mock行为
        when(htmlComparisonService.parseHtmlComparison(any(HtmlComparisonRequestDto.class))).thenReturn(resultDto);

        // 执行测试方法
        HtmlComparisonResultDto result = htmlComparisonComponent.parseHtmlComparison(flowId, baselineServer, targetServer, description);

        // 验证结果
        assertNotNull(result);
        assertEquals(resultDto, result);

        // 验证服务调用
        verify(htmlComparisonService).parseHtmlComparison(any(HtmlComparisonRequestDto.class));
    }

    @Test
    @DisplayName("测试parseHtmlComparison方法 - 带完整信息但flowId为null")
    void testParseHtmlComparison_WithFullInfo_NullFlowId() throws ContrastBusinessException {
        String baselineServer = "基线服务器";
        String targetServer = "目标服务器";
        String description = "测试描述";

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, 
            () -> htmlComparisonComponent.parseHtmlComparison(null, baselineServer, targetServer, description));

        // 验证异常信息
        assertTrue(exception.getMessage().contains("flowId不能为空"));

        // 验证服务未被调用
        verify(htmlComparisonService, never()).parseHtmlComparison(any(HtmlComparisonRequestDto.class));
    }

    @Test
    @DisplayName("测试parseAndExport方法 - 成功场景")
    void testParseAndExport_Success() throws ContrastBusinessException {
        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setFlowId(123L);
        request.setBaselineServer("基线服务器");
        request.setTargetServer("目标服务器");

        // 设置Mock行为
        doNothing().when(htmlComparisonService).exportHtmlComparisonResult(any(HtmlComparisonRequestDto.class), any(HttpServletResponse.class));

        // 执行测试方法
        assertDoesNotThrow(() -> htmlComparisonComponent.parseAndExport(request, response));

        // 验证服务调用
        verify(htmlComparisonService).exportHtmlComparisonResult(request, response);
    }

    @Test
    @DisplayName("测试parseAndExport方法 - flowId为null")
    void testParseAndExport_NullFlowId() throws ContrastBusinessException {
        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setFlowId(null);

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, 
            () -> htmlComparisonComponent.parseAndExport(request, response));

        // 验证异常信息
        assertTrue(exception.getMessage().contains("flowId为空"));

        // 验证服务未被调用
        verify(htmlComparisonService, never()).exportHtmlComparisonResult(any(HtmlComparisonRequestDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试parseAndExport方法 - 带IP信息")
    void testParseAndExport_WithIpInfo() throws ContrastBusinessException {
        Long flowId = 123L;
        String baselineServer = "基线服务器";
        String targetServer = "目标服务器";
        String baseServerIp = "***********";
        String targetServerIp = "***********";

        // 设置Mock行为
        doNothing().when(htmlComparisonService).exportHtmlComparisonResult(any(HtmlComparisonRequestDto.class), any(HttpServletResponse.class));

        // 执行测试方法
        assertDoesNotThrow(() -> htmlComparisonComponent.parseAndExport(flowId, baselineServer, targetServer, baseServerIp, targetServerIp, response));

        // 验证服务调用
        verify(htmlComparisonService).exportHtmlComparisonResult(any(HtmlComparisonRequestDto.class), eq(response));
    }

    @Test
    @DisplayName("测试parseAndExport方法 - 带IP信息但flowId为null")
    void testParseAndExport_WithIpInfo_NullFlowId() throws ContrastBusinessException {
        String baselineServer = "基线服务器";
        String targetServer = "目标服务器";
        String baseServerIp = "***********";
        String targetServerIp = "***********";

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, 
            () -> htmlComparisonComponent.parseAndExport(null, baselineServer, targetServer, baseServerIp, targetServerIp, response));

        // 验证异常信息
        assertTrue(exception.getMessage().contains("flowId不能为空"));

        // 验证服务未被调用
        verify(htmlComparisonService, never()).exportHtmlComparisonResult(any(HtmlComparisonRequestDto.class), any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试getComparisonSummary方法 - 正常结果")
    void testGetComparisonSummary_NormalResult() {
        // 设置比率
        resultDto.setConsistentRate(java.math.BigDecimal.valueOf(80.0));
        resultDto.setInconsistentRate(java.math.BigDecimal.valueOf(10.0));
        resultDto.setMissingRate(java.math.BigDecimal.valueOf(10.0));
        resultDto.setExtraRate(java.math.BigDecimal.valueOf(0.0));

        // 执行测试方法
        String summary = htmlComparisonComponent.getComparisonSummary(resultDto);

        // 验证结果
        assertNotNull(summary);
        assertTrue(summary.contains("比对完成"));
        assertTrue(summary.contains("基线文件：10个"));
        assertTrue(summary.contains("目标文件：10个"));
        assertTrue(summary.contains("一致：8个"));
        assertTrue(summary.contains("不一致：1个"));
        assertTrue(summary.contains("缺失：1个"));
        assertTrue(summary.contains("多出：0个"));
    }

    @Test
    @DisplayName("测试getComparisonSummary方法 - 结果为null")
    void testGetComparisonSummary_NullResult() {
        // 执行测试方法
        String summary = htmlComparisonComponent.getComparisonSummary(null);

        // 验证结果
        assertEquals("比对结果为空", summary);
    }

    @Test
    @DisplayName("测试validateInput方法 - 有效flowId")
    void testValidateInput_ValidFlowId() {
        // 执行测试方法
        boolean result = htmlComparisonComponent.validateInput(123L);

        // 验证结果
        assertTrue(result);
    }

    @Test
    @DisplayName("测试validateInput方法 - flowId为null")
    void testValidateInput_NullFlowId() {
        // 执行测试方法
        boolean result = htmlComparisonComponent.validateInput(null);

        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试getComparisonAdvice方法 - 结果为null")
    void testGetComparisonAdvice_NullResult() {
        // 执行测试方法
        String advice = htmlComparisonComponent.getComparisonAdvice(null);

        // 验证结果
        assertEquals("无法提供建议：比对结果为空", advice);
    }

    @Test
    @DisplayName("测试getComparisonAdvice方法 - 无文件")
    void testGetComparisonAdvice_NoFiles() {
        resultDto.setTotalSourceFiles(0);

        // 执行测试方法
        String advice = htmlComparisonComponent.getComparisonAdvice(resultDto);

        // 验证结果
        assertEquals("无文件需要比对", advice);
    }

    @Test
    @DisplayName("测试getComparisonAdvice方法 - 差异率超过50%")
    void testGetComparisonAdvice_HighDifferenceRate() {
        resultDto.setTotalSourceFiles(10);
        resultDto.setInconsistentCount(6);
        resultDto.setMissingCount(0);

        // 执行测试方法
        String advice = htmlComparisonComponent.getComparisonAdvice(resultDto);

        // 验证结果
        assertTrue(advice.contains("差异率超过50%"));
        assertTrue(advice.contains("建议全面检查环境配置"));
    }

    @Test
    @DisplayName("测试getComparisonAdvice方法 - 差异率在20-50%之间")
    void testGetComparisonAdvice_MediumDifferenceRate() {
        resultDto.setTotalSourceFiles(10);
        resultDto.setInconsistentCount(3);
        resultDto.setMissingCount(0);

        // 执行测试方法
        String advice = htmlComparisonComponent.getComparisonAdvice(resultDto);

        // 验证结果
        assertTrue(advice.contains("差异率在20-50%之间"));
        assertTrue(advice.contains("建议重点关注关键文件"));
    }

    @Test
    @DisplayName("测试getComparisonAdvice方法 - 差异率在5-20%之间")
    void testGetComparisonAdvice_LowMediumDifferenceRate() {
        resultDto.setTotalSourceFiles(10);
        resultDto.setInconsistentCount(1);
        resultDto.setMissingCount(0);

        // 执行测试方法
        String advice = htmlComparisonComponent.getComparisonAdvice(resultDto);

        // 验证结果
        assertTrue(advice.contains("差异率在5-20%之间"));
        assertTrue(advice.contains("环境基本一致"));
    }

    @Test
    @DisplayName("测试getComparisonAdvice方法 - 差异率低于5%")
    void testGetComparisonAdvice_VeryLowDifferenceRate() {
        resultDto.setTotalSourceFiles(100);
        resultDto.setInconsistentCount(2);
        resultDto.setMissingCount(0);

        // 执行测试方法
        String advice = htmlComparisonComponent.getComparisonAdvice(resultDto);

        // 验证结果
        assertTrue(advice.contains("差异率低于5%"));
        assertTrue(advice.contains("环境高度一致"));
    }

    @Test
    @DisplayName("测试构造函数")
    void testConstructor() {
        // 创建新的组件实例
        HtmlComparisonComponent component = new HtmlComparisonComponent(htmlComparisonService);
        
        // 验证实例创建成功
        assertNotNull(component);
    }
}
